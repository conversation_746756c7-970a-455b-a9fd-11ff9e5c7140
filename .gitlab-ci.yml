image: mcr.microsoft.com/playwright:v1.54.1-noble

cache:
  paths:
    - node_modules

variables:
  FORCE_CI: "false"

workflow:
  rules:
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
      when: always
    - if: "$FORCE_CI != 'true'"
      when: never
    - when: always

stages:
  - ⚙️build
  - 🧪 test

build:
  stage: ⚙️build
  script:
    - node --version
    - npm --version
    - npm install
    - ls -la
    - npm run prettier
    - npm run build
    - ls -la ./dist/
  artifacts:
    paths:
      - dist
    expire_in: 2 weeks
  timeout: 30m

unit-tests:
  stage: 🧪 test
  script:
    - npm install
    - npm run test:ci
  timeout: 10m

e2e-tests:
  stage: 🧪 test
  script:
    - ls -la
    - npx playwright install --with-deps
    - node --version
    - npm run e2e:ci
  artifacts:
    when: always
    paths:
      - playwright-report
      - test-results
    expire_in: 1 day
  timeout: 10m

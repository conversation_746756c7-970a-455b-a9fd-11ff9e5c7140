image: mcr.microsoft.com/playwright:v1.40.0-focal

cache:
  paths:
    - node_modules

stages:
  - ⚙️build
  - 🧪 test

build:
  stage: ⚙️build
  rules:
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
      when: on_success
  script:
    - node --version
    - npm --version
    - npm install
    - ls -la
    - npm run prettier
    - npm run build
    - ls -la ./dist/
  artifacts:
    paths:
      - dist
    expire_in: 2 weeks
  timeout: 30m

unit-tests:
  stage: 🧪 test
  rules:
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
      when: on_success
  script:
    - npm install
    - npm run test:ci
  timeout: 10m

e2e-tests:
  stage: 🧪 test
  rules:
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
      when: on_success
  script:
    - ls -la
    - npx playwright install --with-deps
    - node --version
    - npm run e2e:ci
  artifacts:
    when: always
    paths:
      - playwright-report
      - test-results
    expire_in: 1 day
  timeout: 10m

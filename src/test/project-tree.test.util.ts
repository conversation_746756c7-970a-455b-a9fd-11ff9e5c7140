import {
  Project,
  ProjectFolder,
  ProjectItem,
  ProjectItemFolder,
  ProjectItemRequest,
  ProjectItemType,
} from '../app/model/project';
import {
  PayloadType,
  RawPayloadType,
  RequestMethod,
  RequestPayload,
  Request,
} from '../app/model/request';
import { TreeNode } from '../app/ui/_common/tree/tree-node';
import {
  ProjectsTreeService,
  TreeNodeType,
} from '../app/ui/navigator/projects/projects-tree.service';

export namespace ProjectTreeTestUtils {
  abstract class ItemsBuilder {
    items: ProjectItem[] = [];

    withFolder(folder: ProjectFolder): this {
      const folderItem: ProjectItemFolder = {
        type: ProjectItemType.FOLDER,
        value: folder,
      };
      this.items.push(folderItem);
      return this;
    }

    withRequest(request: Request): this {
      const projectItemRequest: ProjectItemRequest = {
        type: ProjectItemType.REQUEST,
        value: request,
      };
      this.items.push(projectItemRequest);
      return this;
    }
  }

  class Project<PERSON>uilder extends ItemsBuilder implements Project {
    id: string;
    name: string;

    constructor(id: string) {
      super();
      this.id = id;
      this.name = `Project ${id}`;
    }
  }

  class FolderBuilder extends ItemsBuilder implements ProjectFolder {
    id: string;
    name: string;

    constructor(id: string) {
      super();
      this.id = id;
      this.name = `Folder ${id}`;
    }
  }

  export function project(id: string): ProjectBuilder {
    return new ProjectBuilder(id);
  }

  export function folder(id: string): FolderBuilder {
    return new FolderBuilder(id);
  }

  export function request(id: string, requestOverrides?: Partial<Request>): Request {
    const requestBase: Omit<Request, 'id' | 'name' | 'url'> = {
      method: RequestMethod.GET,
      headers: [],
      queryParams: [],
      payload: {
        type: PayloadType.NONE,
        rawPayload: {
          type: RawPayloadType.JSON,
          body: '',
        },
        formPayload: {
          params: [],
        },
      } as RequestPayload,
      setVariables: [],
    };
    return {
      ...requestBase,
      id,
      name: `Request ${id}`,
      url: `https://request/${id}`,
      ...requestOverrides,
    };
  }

  export function nodeIdProject(project: Project): string {
    return project.id;
  }

  export function nodeIdFolder(folder: ProjectFolder): string {
    return folder.id;
  }

  export function nodeIdRequest(request: Request): string {
    return request.id;
  }

  export function nodeOfProject(project: Project): TreeNode {
    return {
      id: nodeIdProject(project),
      label: project.name,
      icon: ProjectsTreeService.icons.project,
      level: 0,
      data: project,
      type: TreeNodeType.PROJECT_NODE,
    };
  }

  export function nodeOfFolder(folder: ProjectFolder, options: { level: number }): TreeNode {
    return {
      id: nodeIdFolder(folder),
      label: folder.name,
      icon: ProjectsTreeService.icons.folder,
      level: options.level,
      data: folder,
      type: TreeNodeType.PROJECT_FOLDER_NODE,
    };
  }

  export function nodeOfRequest(request: Request, options: { level: number }): TreeNode {
    return {
      id: nodeIdRequest(request),
      label: request.name,
      extraInfo: [
        {
          info: request.method,
          style: `projects-tree-info-method-${request.method}`,
        },
        {
          info: new URL(request.url).pathname,
          style: 'projects-tree-path',
        },
      ],
      icon: ProjectsTreeService.icons.request,
      level: options.level,
      data: request,
      type: TreeNodeType.REQUEST_NODE,
    };
  }
}

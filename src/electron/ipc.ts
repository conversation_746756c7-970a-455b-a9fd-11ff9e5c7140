import { ipcMain, IpcMainEvent } from 'electron';
import { logger } from './logger';
import { currentWindow } from './main';

const ipcResponseSuffix = 'response';

function ipcRegisterFunction<T>(service: any, serviceName: string, func: Function) {
  const ipcName = `${serviceName}.${func.name}`;
  logger.debug(`IPC: ${ipcName}()`);

  ipcMain.on(ipcName, (event: IpcMainEvent, ...args: any) => {
    logger.debug(`CALL: ${ipcName}`, args);

    const callId: string = args[0];
    const callArgs = args[1];

    const sender = new IpcSender(currentWindow(), ipcName, callId);

    try {
      const result = func.apply(service, Object.values(callArgs)) as Promise<T>;
      result.then((t) => sender.send(t)).catch((err) => sender.error(err));
    } catch (err) {
      sender.error(err);
    }
  });
}

export function ipcRegisterService<T>(service: any, serviceName: string, functions: Function[]) {
  functions.forEach((f) => ipcRegisterFunction(service, serviceName, f));
}

class IpcSender {
  constructor(
    private readonly win: Electron.BrowserWindow,
    private readonly ipcName: string,
    private readonly callId: string,
  ) {}
  private responseChannel = `${this.ipcName}.${ipcResponseSuffix}.${this.callId}`;

  send(value: any) {
    logger.debug(`RESP: ${this.ipcName} [${this.responseChannel}]`, value);
    this.sendValue(value);
  }

  error(err: any) {
    logger.debug(`ERR: ${this.ipcName} [${this.responseChannel}]`, err);
    const message = err.message ? err.message : err;
    this.sendValue({ _error: message });
  }

  private sendValue(value: any) {
    this.win.webContents.send(this.responseChannel, value);
  }
}

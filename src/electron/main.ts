import { app, BrowserWindow } from 'electron';
import * as path from 'path';
import { isDev, logger } from './logger';
import { AppConfig, SystemService } from './system/system-service';
import { HttpClientService } from './http/http-client-service';
import { setupAppMenu } from './app-menu';

if (isDev()) {
  logger.info('Running in DEV mode');
} else {
  logger.info('© 2025 Neovibrant LTD, All rights reserved');
}

const dist = path.join(__dirname, '../..', 'dist');

let win: BrowserWindow | null = null;
let appConfig: AppConfig | null = null;

function createWindow() {
  win = new BrowserWindow({
    width: appConfig?.windowState?.width ?? 960,
    height: appConfig?.windowState?.height ?? 680,
    minWidth: 800,
    minHeight: 600,
    backgroundColor: '#ffffff',
    icon: `file://${dist}/assets/logo.png`,
    autoHideMenuBar: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      sandbox: false,
    },
  });

  if (appConfig?.windowState?.isMaximized === true) {
    win.maximize();
  }

  isDev()
    ? win.loadURL('http://localhost:4200')
    : win.loadFile(`${dist}/kingston-app/browser/index.html`);

  if (isDev()) {
    void win.webContents.executeJavaScript('window.KINGSTON_DEV = true');
  }

  win.on('close', () => {
    if (win != null) {
      const { width, height } = win.getBounds();
      appConfig = {
        ...appConfig,
        windowState: {
          width: win.isMaximized() ? appConfig?.windowState?.width : width,
          height: win.isMaximized() ? appConfig?.windowState?.height : height,
          isMaximized: win.isMaximized(),
        },
      };
      systemService.saveAppConfig(appConfig);
    }
  });

  win.on('closed', function () {
    win = null;
  });
}

app.whenReady().then(() => {
  setupAppMenu();
  initServices();
  appConfig = systemService.loadAppConfig();
  createWindow();
});

app.on('window-all-closed', function () {
  app.quit();
});

app.on('activate', function () {
  if (win === null) {
    createWindow();
  }
});

export function currentWindow(): BrowserWindow {
  if (!win) {
    logger.error("Attempting to get 'win' before it's initialised");
  }
  return win!!;
}
export let systemService: SystemService;

export let httpClientService: HttpClientService;

function initServices() {
  logger.debug('Initialising services...');
  systemService = new SystemService();
  httpClientService = new HttpClientService();
  logger.debug('Finished initialising services.');
}

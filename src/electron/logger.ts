import { Logger } from 'tslog';

let dev: boolean | null = null;
export function isDev(): boolean {
  if (dev === null) {
    dev = !!process.argv?.find((arg) => arg === '--dev') || !!process.env['KINGSTON_DEV'];
  }
  return dev;
}

export const logger = new Logger({
  // tslog comes with default log level 0: silly, 1: trace, 2: debug, 3: info, 4: warn, 5: error, 6: fatal.
  minLevel: isDev() ? 2 : 3,
});

import { ipcRegisterService } from '../ipc';
import { logger } from '../logger';
import { ISystemService, SystemServiceName, UserMessage } from '../../common/system';
import { app, dialog } from 'electron';
import { currentWindow } from '../main';
import * as fs from 'fs';
import * as path from 'path';

const APP_CONFIG_FILE_NAME = 'app-config.json';

export class SystemService implements ISystemService {
  #userDataPath: string;

  constructor() {
    logger.debug(`Setting up ${SystemServiceName} service...`);
    ipcRegisterService(this, SystemServiceName, [
      this.ping,
      this.messageUser,
      this.loadValue,
      this.saveValue,
    ]);

    this.#userDataPath = app.getPath('userData');
    logger.debug('UserData dir', this.#userDataPath);
  }

  ping(): Promise<any> {
    return Promise.resolve({
      name: 'pong',
      async: true,
    });
  }

  messageUser(message: UserMessage): Promise<boolean> {
    let type: 'none' | 'info' | 'error' | 'question' | 'warning';
    switch (message.messageType) {
      case 'error':
        type = 'error';
        break;
      case 'question':
        type = 'question';
        break;
      case 'info':
        type = 'info';
        break;
    }
    return dialog
      .showMessageBox(currentWindow(), {
        message: message.message,
        title: message.title,
        detail: message.detail,
        type,
        buttons: [message.confirm, message.cancel].filter(Boolean).map((b) => b as string),
      })
      .then((value) => value.response === 0);
  }

  loadValue(name: string): Promise<string | undefined> {
    const filePath = this.userDataFile(name);
    return new Promise((resolve) => {
      if (filePath == null) {
        resolve(undefined);
        return;
      }
      fs.readFile(filePath, (err, data) => {
        if (err) {
          logger.error('Error reading value from file', err);
          resolve(undefined);
        } else {
          const stringValue = data?.toString();
          if (stringValue == null || stringValue.trim().length === 0) {
            resolve(undefined);
          } else {
            resolve(stringValue);
          }
        }
      });
    });
  }

  saveValue(name: string, value: string): Promise<void> {
    const filePath = this.userDataFile(name);
    return new Promise((resolve, reject) => {
      if (filePath == null) {
        reject('Error writing value to file');
        return;
      }
      fs.writeFile(filePath, value, (err) => {
        if (err) {
          logger.error('Error writing value to file', err);
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  private userDataFile(name: string): string | undefined {
    const dirPath = this.#userDataPath;
    const filePath = path.resolve(dirPath, `${name}.json`);
    if (!filePath.startsWith(dirPath)) {
      logger.error('Invalid file path', name);
      return undefined;
    } else {
      return filePath;
    }
  }

  loadAppConfig(): AppConfig | null {
    try {
      const filePath = path.resolve(this.#userDataPath, APP_CONFIG_FILE_NAME);
      const appConfigJson = fs.readFileSync(filePath, {
        encoding: 'utf8',
      });
      const appConfig = JSON.parse(appConfigJson);
      logger.debug('Loaded appConfig');
      return appConfig;
    } catch (e) {
      logger.warn('Error loading appConfig', e);
      return null;
    }
  }

  saveAppConfig(appConfig: AppConfig) {
    logger.info('Saving appConfig...');
    try {
      const filePath = path.resolve(this.#userDataPath, APP_CONFIG_FILE_NAME);
      const appConfigJson = JSON.stringify(appConfig);
      fs.writeFileSync(filePath, appConfigJson, {
        encoding: 'utf8',
      });
    } catch (e) {
      logger.error('Error saving appConfig', e);
    }
  }
}

export type AppConfig = {
  windowState: WindowState;
};

export type WindowState = {
  width?: number;
  height?: number;
  isMaximized: boolean;
};

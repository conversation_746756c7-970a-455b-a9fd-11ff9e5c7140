import { app, dialog, Menu, MenuItemConstructorOptions, shell } from 'electron';
import { logger } from './logger';
import { currentWindow } from './main';

export function setupAppMenu() {
  const template: Array<MenuItemConstructorOptions> = [
    {
      label: 'File',
      submenu: [{ role: 'quit' }],
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'pasteAndMatchStyle' },
        { role: 'delete' },
        { role: 'selectAll' },
      ],
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' },
      ],
    },
    {
      role: 'window',
      submenu: [{ role: 'minimize' }, { role: 'close' }],
    },
    {
      role: 'help',
      submenu: [
        {
          label: 'Learn more',
          click: async () => {
            await shell.openExternal('https://headrest.io/help');
          },
        },
      ],
    },
  ];

  if (process.platform === 'darwin') {
    // App menu
    template.unshift({
      label: app.getName(),
      submenu: [
        { role: 'about' },
        { type: 'separator' },
        { role: 'hide' },
        { role: 'hideOthers' },
        { role: 'unhide' },
        { type: 'separator' },
        { role: 'quit' },
      ],
    });

    // Window menu
    template[3].submenu = [
      { role: 'close' },
      { role: 'minimize' },
      { role: 'zoom' },
      { type: 'separator' },
      { role: 'front' },
    ];
  } else {
    // Not on macOS
    // Help menu
    template[4].submenu = [
      {
        label: 'Learn more',
        click: async () => {
          await shell.openExternal('https://headrest.io/help');
        },
      },
      {
        role: 'about',
        click: async () => {
          logger.info('Headrest version: ' + app.getVersion());
          void dialog.showMessageBox(currentWindow(), {
            message: `Headrest ${app.getVersion()}`,
            title: 'About Headrest',
            detail: 'Copyright © 2025 Neovibrant LTD. All rights reserved.',
            type: 'info',
          });
        },
      },
    ];
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

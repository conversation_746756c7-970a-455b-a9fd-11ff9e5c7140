import { logger } from '../logger';
import { ipcRegisterService } from '../ipc';
import {
  HttpClientServiceName,
  HttpRequest,
  HttpResponse,
  HttpResponseHeader,
  IHttpClientService,
} from '../../common/http-client';
import got, { Method } from 'got';

export class HttpClientService implements IHttpClientService {
  constructor() {
    logger.debug(`Setting up ${HttpClientServiceName} service...`);
    ipcRegisterService(this, HttpClientServiceName, [this.exchange]);
  }

  exchange(request: HttpRequest): Promise<HttpResponse> {
    const protocol = request.https ? 'https://' : 'http://';
    const port = request.port ? `:${request.port}` : '';

    const url = `${protocol}${request.hostname}${port}${request.path}${request.query ?? ''}${
      request.hash ?? ''
    }`;
    logger.debug('Requesting url...', url);

    const startTimeMS = new Date().getTime();

    return got({
      method: request.method as Method,
      url,
      headers: request.headers,
      body: request.payload,
      decompress: true,
      followRedirect: true,
      responseType: 'text',
      allowGetBody: true,
      retry: {
        limit: 0,
      },
      timeout: {
        lookup: 10_000,
        connect: 10_000,
        secureConnect: 10_000,
        socket: 10_000,
        send: 10_000,
        response: 10_000,
      },
    })
      .then((response) => {
        logger.debug('Got HTTP response', {
          status: response.statusCode,
          headers: response.headers,
          payload: response.body,
        });
        return this.withRequestInfo(startTimeMS, {
          payload: response.body,
          status: response.statusCode,
          headers: this.headersOfGot(response.headers),
        });
      })
      .catch((e) => {
        if (e.response != null && e.response.statusCode != null && e.response.statusCode > 0) {
          return this.withRequestInfo(startTimeMS, {
            payload: e.response.body,
            status: e.response.statusCode,
            headers: this.headersOfGot(e.response.headers),
          });
        } else {
          logger.warn('Error making the HTTP call', e);
          throw e;
        }
      });
  }

  private headersOfGot(headers: NodeJS.Dict<string | string[]>) {
    return Object.keys(headers).map((headerName) => {
      const headerValue = headers[headerName];
      let values: string[] | undefined;
      if (typeof headerValue === 'string') {
        values = [headerValue];
      } else {
        values = headerValue;
      }
      return {
        name: headerName,
        values,
      } as HttpResponseHeader;
    });
  }

  private withRequestInfo(startTimeMS: number, response: HttpResponse): HttpResponse {
    const endTimeMS = new Date().getTime();
    logger.debug('Request took (ms)', endTimeMS - startTimeMS);
    return {
      ...response,
      info: {
        initiatedAt: startTimeMS,
        completedAt: endTimeMS,
      },
    };
  }
}

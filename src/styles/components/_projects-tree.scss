@use "../colors" as *;
@use "../typography" as *;

.projects--tree {
  .projects-tree-path {
    font-size: $font-size-small;
    @include a-color($color-subtle);
  }

  @mixin projects-tree-info-method {
    font-size: $font-size-small;
    @include a-color($color-subtle);
  }

  .projects-tree-info-method-OPTIONS,
  .projects-tree-info-method-HEAD,
  .projects-tree-info-method-TRACE {
    @include projects-tree-info-method;
  }

  .projects-tree-info-method-GET,
  .projects-tree-info-method-OPTIONS,
  .projects-tree-info-method-HEAD {
    @include projects-tree-info-method;
    @include a-color($color-positive);
  }

  .projects-tree-info-method-POST,
  .projects-tree-info-method-PUT,
  .projects-tree-info-method-PATCH {
    @include projects-tree-info-method;
    @include a-color($color-primary);
  }

  .projects-tree-info-method-DELETE {
    @include projects-tree-info-method;
    @include a-color($color-negative);
  }
}

@use "../colors" as *;
@use "../typography" as *;
@use "sass:color";

$menu-item-height: 3.2rem;

.mat-mdc-menu-panel {
  .mat-mdc-menu-content {
    padding: 0 1rem;
    &:not(:empty) {
      padding: 0;
      margin: 0;
    }
    hr.separator {
      padding: 0;
      margin: 0;
      border: solid 0.1rem $color-struct-border-light;
      @include in-dark {
        border-color: $color-struct-border-dark;
      }
    }
    button.mat-mdc-menu-item {
      height: $menu-item-height;
      min-height: $menu-item-height;
      line-height: $menu-item-height;
      .mdc-list-item__primary-text {
        font-size: $font-size-default;
        font-weight: normal;
      }
      fa-icon {
        min-width: 2rem;
        display: inline-block;
      }
      &:hover:not([disabled]) {
        @include a-bk-color($color-primary-slighter);
      }
      &.negative {
        &:hover:not([disabled]) {
          background-color: color.adjust($color-negative-light, $alpha: -0.9);
          @include in-dark {
            background-color: color.adjust($color-negative-dark, $alpha: -0.9);
          }
        }
        .mdc-list-item__primary-text {
          @include a-color($color-negative);
        }
      }
    }
  }
}

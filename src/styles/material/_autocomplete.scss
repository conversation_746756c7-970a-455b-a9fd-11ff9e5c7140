@use "sass:math";
@use "./menu" as *;
@use "../colors" as *;
@use "../typography" as *;

.cdk-overlay-pane {
  .mdc-menu-surface.mat-mdc-autocomplete-panel {
    padding: 0;
    .mat-mdc-optgroup-label {
      height: math.div($menu-item-height, 1.25);
      min-height: math.div($menu-item-height, 1.25);
      line-height: math.div($menu-item-height, 1.25);
      .mdc-list-item__primary-text {
        font-size: $font-size-small;
        font-weight: bold;
      }
    }
    mat-option.mat-mdc-option.mdc-list-item {
      height: $menu-item-height;
      min-height: $menu-item-height;
      line-height: $menu-item-height;
      font-size: $font-size-default;
      &:hover {
        @include a-bk-color($color-primary-slighter);
      }
    }
  }
}

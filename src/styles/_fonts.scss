@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 200;
  font-display: swap;
  src: url("../assets/fonts/Quicksand-Light.woff2") format("woff2");
}
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../assets/fonts/Quicksand-Regular.woff2") format("woff2");
}
@font-face {
  font-family: "Quicksand";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../assets/fonts/Quicksand-Bold.woff2") format("woff2");
}
@font-face {
  font-family: "Cascadia";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../assets/fonts/CascadiaCode.woff2") format("woff2");
}
@font-face {
  font-family: "Cascadia";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("../assets/fonts/CascadiaCodeItalic.woff2") format("woff2");
}
@font-face {
  font-family: "Courier Prime Code";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../assets/fonts/CourierPrimeCode.woff2") format("woff2");
}
@font-face {
  font-family: "Courier Prime Code";
  font-style: italic;
  font-weight: 400;
  font-display: swap;
  src: url("../assets/fonts/CourierPrimeCode-Italic.woff2") format("woff2");
}

import { TestBed } from '@angular/core/testing';
import { SystemService } from '../system/system.service';
import { UserMessageService } from '../system/user-message.service';
import { VariablesService } from './variables.service';
import { ProjectsService } from '../projects/projects.service';
import { Variables, VariableValues, VariableValueSourceType } from '../../model/variable';
import { Environment, EnvironmentId } from '../../model/environment';
import { EnvironmentsService } from './environments.service';

describe('VariablesService', () => {
  let service: VariablesService;
  let systemService: SystemService;
  let userMessageService: UserMessageService;
  let projectsService: ProjectsService;
  let environmentsService: EnvironmentsService;

  async function setUp(
    testData: {
      variables?: Variables;
      variableValues?: VariableValues;
      environments?: Environment[];
      activeEnvironmentId?: EnvironmentId;
    } = {},
  ) {
    systemService = jasmine.createSpyObj('SystemService', {
      ['loadValue']: Promise.resolve(JSON.stringify(testData.variables)),
      ['saveValue']: Promise.resolve(),
    });
    userMessageService = jasmine.createSpyObj<UserMessageService>('UserMessageService', {
      ['confirm']: Promise.resolve(),
    });
    projectsService = jasmine.createSpyObj<ProjectsService>(
      'ProjectsService',
      {
        ['removeReferencesToVariable']: undefined,
      },
      {
        ['projects']: [project('project-1'), project('project-2')],
      },
    );
    environmentsService = jasmine.createSpyObj<EnvironmentsService>('EnvironmentsService', {
      ['environments']: testData.environments,
      ['activeEnvironment']: testData.environments?.find(
        (e) => e.id === testData.activeEnvironmentId,
      ),
    });
    TestBed.configureTestingModule({
      providers: [
        VariablesService,
        {
          provide: SystemService,
          useValue: systemService,
        },
        {
          provide: ProjectsService,
          useValue: projectsService,
        },
        {
          provide: EnvironmentsService,
          useValue: environmentsService,
        },
        {
          provide: UserMessageService,
          useValue: userMessageService,
        },
      ],
    });
    service = TestBed.inject(VariablesService);
    await service.initialise();
  }

  it('should sort the global variables', async () => {
    await setUp({
      variables: {
        projectScope: {},
        globalScope: [
          {
            key: 'b_var',
            staticValues: { env: 'var1Value' },
          },
          {
            key: 'c_var2',
          },
          {
            key: 'A_var2',
          },
        ],
      },
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    expect(service.variablesInGlobalScope()).toEqual([
      {
        key: 'A_var2',
      },
      {
        key: 'b_var',
        staticValues: { env: 'var1Value' },
      },
      {
        key: 'c_var2',
      },
    ]);
  });

  it('should sort the project variables', async () => {
    await setUp({
      variables: {
        globalScope: [],
        projectScope: {
          'project-2': [
            {
              key: 'b_var',
              staticValues: { env: 'var1Value' },
            },
          ],
          'project-1': [
            {
              key: 'b_var',
              staticValues: { env: 'var1Value' },
            },
            {
              key: 'c_var2',
            },
            {
              key: 'A_var2',
            },
          ],
        },
      },
    });
    expect(service.variablesInProjectScope(project('project-1'))).toEqual([
      {
        key: 'A_var2',
      },
      {
        key: 'b_var',
        staticValues: { env: 'var1Value' },
      },
      {
        key: 'c_var2',
      },
    ]);
  });

  it('should evaluate variable and replace in string', async () => {
    await setUp({
      variables: {
        projectScope: {
          'project-1': [
            {
              key: 'var1',
              staticValues: { env: 'var1ProjectValue' },
            },
          ],
        },
        globalScope: [
          {
            key: 'var1',
            staticValues: { env: 'var1Value' },
          },
          {
            key: 'var2',
          },
        ],
      },
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    expect(service.evaluateVariable('var1', undefined, 'env')).toEqual('var1Value');
    expect(service.evaluateStringWithVariables('Some {{var1}}', undefined, 'env')).toEqual(
      'Some var1Value',
    );

    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1ProjectValue');
    expect(service.evaluateStringWithVariables('Some {{var1}}', 'project-1', 'env')).toEqual(
      'Some var1ProjectValue',
    );
  });

  it('should replace null variable with empty string', async () => {
    await setUp({
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    expect(service.evaluateVariable('var1', undefined, 'env')).toEqual(undefined);
    expect(service.evaluateStringWithVariables('Some {{var1}}', undefined, 'env')).toEqual('Some ');
  });

  it('should create project variable, set value and evaluate', async () => {
    await setUp({
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    await service.createProjectVariable(
      {
        key: 'var1',
        staticValues: { env: 'var1ProjectValue' },
      },
      project('project-1'),
    );

    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1ProjectValue');

    await service.setVariableValue('var1', 'project-1', 'env', {
      value: 'var1ProjectValue2',
      sourceId: '123',
      sourceType: VariableValueSourceType.REQUEST_SESSION,
    });

    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1ProjectValue2');
  });

  it('should create global variable, set value and evaluate', async () => {
    await setUp({
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    await service.createGlobalVariable({
      key: 'var1',
      staticValues: { env: 'var1Value' },
    });

    expect(service.evaluateVariable('var1', undefined, 'env')).toEqual('var1Value');
    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1Value');

    await service.setVariableValue('var1', undefined, 'env', {
      value: 'var1Value2',
      sourceId: '123',
      sourceType: VariableValueSourceType.REQUEST_SESSION,
    });

    expect(service.evaluateVariable('var1', undefined, 'env')).toEqual('var1Value2');
    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1Value2');
  });

  it('should delete project variable and remove references from all projects', async () => {
    await setUp({
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    await service.createProjectVariable(
      {
        key: 'var1',
        staticValues: { env: 'var1ProjectValue' },
      },
      project('project-1'),
    );

    await service.deleteProjectVariable('var1', project('project-1'));

    expect(projectsService.removeReferencesToVariable).toHaveBeenCalledWith(
      'var1',
      project('project-1'),
    );
    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual(undefined);
  });

  it('should delete project variable and not remove references if also present in the global scope', async () => {
    await setUp({
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    await service.createProjectVariable(
      {
        key: 'var1',
        staticValues: { env: 'var1ProjectValue' },
      },
      project('project-1'),
    );
    await service.createGlobalVariable({
      key: 'var1',
      staticValues: { env: 'var1Value' },
    });

    await service.deleteProjectVariable('var1', project('project-1'));

    expect(projectsService.removeReferencesToVariable).not.toHaveBeenCalledWith(
      'var1',
      project('project-1'),
    );
    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1Value');
  });

  it(`should delete global variable and remove references from all projects that don't also define it`, async () => {
    await setUp({
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    await service.createGlobalVariable({
      key: 'var1',
      staticValues: { env: 'var1Value' },
    });
    await service.createProjectVariable(
      {
        key: 'var1',
        staticValues: { env: 'var1ProjectValue' },
      },
      project('project-1'),
    );

    await service.deleteGlobalVariable('var1');

    expect(projectsService.removeReferencesToVariable).toHaveBeenCalledWith(
      'var1',
      project('project-2'),
    );
    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1ProjectValue');
  });

  it('should set and unset project variable', async () => {
    await setUp({
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    await service.createProjectVariable(
      {
        key: 'var1',
        staticValues: { env: 'var1ProjectStaticValue' },
      },
      project('project-1'),
    );
    await service.setVariableValue('var1', 'project-1', 'env', {
      value: 'var1ProjectValue2',
      sourceId: '123',
      sourceType: VariableValueSourceType.REQUEST_SESSION,
    });
    await service.unsetVariableValue(
      'var1',
      'project-1',
      '123',
      VariableValueSourceType.REQUEST_SESSION,
    );

    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1ProjectStaticValue');
  });

  it('should set and unset global variable', async () => {
    await setUp({
      environments: [
        {
          id: 'env',
          name: 'env',
        },
      ],
      activeEnvironmentId: 'env',
    });
    await service.createGlobalVariable({
      key: 'var1',
      staticValues: { env: 'var1StaticValue' },
    });
    await service.setVariableValue('var1', 'project-1', 'env', {
      value: 'var1GlobalValue',
      sourceId: '123',
      sourceType: VariableValueSourceType.REQUEST_SESSION,
    });
    await service.unsetVariableValue(
      'var1',
      'project-1',
      '123',
      VariableValueSourceType.REQUEST_SESSION,
    );

    expect(service.evaluateVariable('var1', 'project-1', 'env')).toEqual('var1StaticValue');
  });

  it('should evaluate project variable for several environments', async () => {
    await setUp({
      environments: [
        {
          id: 'env1',
          name: 'env1',
        },
        {
          id: 'env2',
          name: 'env2',
        },
      ],
      activeEnvironmentId: 'env2',
    });

    await service.createProjectVariable(
      {
        key: 'var1',
        staticValues: { env1: 'var1ProjectStaticValueE1', env2: 'var1ProjectStaticValueE2' },
      },
      project('project-1'),
    );

    expect(service.evaluateVariable('var1', 'project-1', 'env1')).toEqual(
      'var1ProjectStaticValueE1',
    );
    expect(service.evaluateVariable('var1', 'project-1', 'env2')).toEqual(
      'var1ProjectStaticValueE2',
    );

    await service.setVariableValue('var1', 'project-1', 'env1', {
      value: 'var1ProjectValueCustomE1',
      sourceId: '123',
      sourceType: VariableValueSourceType.REQUEST_SESSION,
    });
    await service.setVariableValue('var1', 'project-1', 'env2', {
      value: 'var1ProjectValueCustomE2',
      sourceId: '123',
      sourceType: VariableValueSourceType.REQUEST_SESSION,
    });

    expect(service.evaluateVariable('var1', 'project-1', 'env1')).toEqual(
      'var1ProjectValueCustomE1',
    );
    expect(service.evaluateVariable('var1', 'project-1', 'env2')).toEqual(
      'var1ProjectValueCustomE2',
    );
  });

  it('should evaluate global variable for several environments', async () => {
    await setUp({
      environments: [
        {
          id: 'env1',
          name: 'env1',
        },
        {
          id: 'env2',
          name: 'env2',
        },
      ],
      activeEnvironmentId: 'env2',
    });

    await service.createGlobalVariable({
      key: 'var1',
      staticValues: { env1: 'var1ProjectStaticValueE1', env2: 'var1ProjectStaticValueE2' },
    });

    expect(service.evaluateVariable('var1', undefined, 'env1')).toEqual('var1ProjectStaticValueE1');
    expect(service.evaluateVariable('var1', undefined, 'env2')).toEqual('var1ProjectStaticValueE2');

    await service.setVariableValue('var1', undefined, 'env1', {
      value: 'var1ProjectValueCustomE1',
      sourceId: '123',
      sourceType: VariableValueSourceType.REQUEST_SESSION,
    });
    await service.setVariableValue('var1', undefined, 'env2', {
      value: 'var1ProjectValueCustomE2',
      sourceId: '123',
      sourceType: VariableValueSourceType.REQUEST_SESSION,
    });

    expect(service.evaluateVariable('var1', undefined, 'env1')).toEqual('var1ProjectValueCustomE1');
    expect(service.evaluateVariable('var1', undefined, 'env2')).toEqual('var1ProjectValueCustomE2');
  });
});

function project(projectId: string) {
  return {
    id: projectId,
    name: 'Project 1',
    items: [],
  };
}

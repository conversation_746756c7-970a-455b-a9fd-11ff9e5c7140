import { Injectable } from '@angular/core';
import { SystemService } from '../system/system.service';
import { createDefaultEnvironment, Environment, EnvironmentsState } from '../../model/environment';
import { UserMessageService } from '../system/user-message.service';
import { v4 as uuidv4 } from 'uuid';

const ENVIRONMENTS = 'environments';
const ENVIRONMENTS_STATE = 'environments-state';

@Injectable()
export class EnvironmentsService {
  #environments: Environment[] & { 0: Environment };
  #environmentsState: EnvironmentsState;

  constructor(
    private readonly system: SystemService,
    private readonly messageService: UserMessageService,
  ) {
    const defaultEnvironment = createDefaultEnvironment();
    this.#environments = [defaultEnvironment];
    this.#environmentsState = {
      activeEnvironmentId: defaultEnvironment.id,
    };
  }

  async initialise() {
    await this.loadEnvironments();
    await this.loadEnvironmentsState();
  }

  get environments(): Environment[] {
    return [...this.#environments].sort((a, b) =>
      a.name.toLowerCase().localeCompare(b.name.toLowerCase()),
    );
  }

  private async loadEnvironments() {
    const environmentsJson = await this.system.loadValue(ENVIRONMENTS);
    if (environmentsJson != null && environmentsJson.length > 0) {
      try {
        this.#environments = JSON.parse(environmentsJson);
      } catch (e) {
        console.error(`Error parsing environments after loading them from system`, e);
      }
    } else {
      await this.saveEnvironments(); // Save the default environment
    }
  }

  private async loadEnvironmentsState() {
    const environmentsStateJson = await this.system.loadValue(ENVIRONMENTS_STATE);
    if (environmentsStateJson != null) {
      try {
        this.#environmentsState = JSON.parse(environmentsStateJson);
      } catch (e) {
        console.error(`Error parsing environments state after loading them from system`, e);
      }
    } else {
      await this.saveEnvironmentsState(); // Save the default state
    }
  }

  get activeEnvironment(): Environment {
    const activeEnvironment = this.#environments.find(
      (environment) => environment.id === this.#environmentsState.activeEnvironmentId,
    );
    if (activeEnvironment != null) {
      return activeEnvironment;
    } else {
      const firstEnvironment = this.#environments[0];
      void this.select(firstEnvironment);
      return firstEnvironment;
    }
  }

  async select(environment: Environment) {
    this.#environmentsState.activeEnvironmentId = environment.id;
    await this.saveEnvironmentsState();
  }

  async createEnvironment(environmentName: string) {
    const existingEnvironment = this.#environments.some((e) => e.name === environmentName);
    if (existingEnvironment) {
      throw new Error('An environment with the same name already exists');
    }
    this.#environments.push({
      id: uuidv4(),
      name: environmentName,
    });
    await this.saveEnvironments();
  }

  async renameEnvironment(environment: Environment) {
    const existingEnvironment = this.#environments
      .filter((e) => e.id !== environment.id)
      .some((e) => e.name === environment.name);
    if (existingEnvironment) {
      throw new Error('Another environment with the same name already exists');
    }
    const index = this.#environments.findIndex((e) => e.id === environment.id);
    if (index < 0) {
      throw new Error('Cannot find environment to rename');
    }
    this.#environments[index].name = environment.name;
    await this.saveEnvironments();
  }

  async deleteEnvironment(environmentId: string) {
    if (this.#environments.length === 1) {
      throw new Error('Cannot delete the last environment');
    }

    if (this.activeEnvironment.id === environmentId) {
      throw new Error('Cannot delete the active environment');
    }

    const index = this.#environments.findIndex((e) => e.id === environmentId);
    if (index < 0) {
      throw new Error('Cannot find environment to delete');
    }
    this.#environments.splice(index, 1);

    await this.saveEnvironments();
  }

  private async saveEnvironments() {
    try {
      await this.system.saveValue(ENVIRONMENTS, JSON.stringify(this.#environments));
    } catch (e: any) {
      await this.messageService.error({
        title: 'Error saving environments',
        message: 'The latest change may not be saved',
        detail: e?.message,
      });
    }
  }

  private async saveEnvironmentsState() {
    try {
      await this.system.saveValue(ENVIRONMENTS_STATE, JSON.stringify(this.#environmentsState));
    } catch (e: any) {
      await this.messageService.error({
        title: 'Error saving environment state',
        message: 'The active environment may not be saved',
        detail: e?.message,
      });
    }
  }
}

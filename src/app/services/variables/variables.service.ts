import { Injectable } from '@angular/core';
import {
  Variable,
  Variables,
  VariableValue,
  VariableValues,
  VariableValueSlot,
  VariableValueSourceType,
} from '../../model/variable';
import { SystemService } from '../system/system.service';
import { UserMessageService } from '../system/user-message.service';
import { Project } from '../../model/project';
import { ProjectsService } from '../projects/projects.service';
import { EnvironmentId } from '../../model/environment';
import { EnvironmentsService } from './environments.service';

const VARIABLES = 'variables';
const VARIABLE_VALUES = 'variable-values';

@Injectable()
export class VariablesService {
  #variables: Variables;
  #variableValues: VariableValues;

  constructor(
    private readonly system: SystemService,
    private readonly projectsService: ProjectsService,
    private readonly environmentsService: EnvironmentsService,
    private readonly messageService: UserMessageService,
  ) {
    this.#variables = {
      globalScope: [],
      projectScope: {},
    };
    this.#variableValues = {
      globalScope: {},
      projectScope: {},
    };
  }

  async initialise() {
    await this.loadVariables();
    await this.loadVariableValues();
  }

  private async loadVariables() {
    const variablesJson = await this.system.loadValue(VARIABLES);
    if (variablesJson != null) {
      try {
        this.#variables = JSON.parse(variablesJson);
        await this.migrateVariables();
      } catch (e) {
        console.error(`Error parsing variables after loading them from system`, e);
      }
    }
  }

  private async migrateVariables() {
    console.debug('Migrating variables to new staticValues format...');
    const environmentId = this.environmentsService.activeEnvironment.id;
    let migrated = false;

    type VariableMigration = Variable & {
      staticValue?: VariableValue; // old field to migrate
    };

    function migrateStaticValues(variables: VariableMigration[]) {
      for (const variable of variables) {
        if (variable.staticValue != null) {
          console.info('Migrating staticValue to staticValues', variable.key);
          variable.staticValues = {
            [environmentId]: variable.staticValue,
          };
          delete variable.staticValue;
          migrated = true;
        }
      }
    }

    for (const projectId of Object.keys(this.#variables.projectScope)) {
      migrateStaticValues(this.#variables.projectScope[projectId]);
    }
    migrateStaticValues(this.#variables.globalScope);

    if (migrated) {
      await this.saveVariables();
    }
  }

  private async loadVariableValues() {
    const variableValuesJson = await this.system.loadValue(VARIABLE_VALUES);
    if (variableValuesJson != null) {
      try {
        this.#variableValues = JSON.parse(variableValuesJson);
      } catch (e) {
        console.error(`Error parsing variable values after loading them from system`, e);
      }
    }
  }

  variablesInGlobalScope(): Variable[] {
    return [...this.#variables.globalScope].sort((a, b) =>
      a.key.toLowerCase().localeCompare(b.key.toLowerCase()),
    );
  }

  variablesInProjectScope(project?: Project): Variable[] {
    const projectVariables =
      project?.id != null ? (this.#variables.projectScope[project.id] ?? []) : [];
    return [...projectVariables].sort((a, b) =>
      a.key.toLowerCase().localeCompare(b.key.toLowerCase()),
    );
  }

  evaluateStringWithVariables(
    stringWithVars: string,
    projectId: string | undefined,
    environmentId: EnvironmentId,
  ): string {
    if (!stringWithVars.includes('{{')) {
      return stringWithVars;
    }

    const vars = new Array<string>();

    // noinspection RegExpRedundantEscape
    const varRegex = /\{\{[a-zA-Z0-9-_]+\}\}/g;
    while (true) {
      const match = varRegex.exec(stringWithVars);
      if (match == null) {
        break;
      }
      if (match[0] != null) {
        vars.push(match[0].slice(2, -2));
      }
    }

    let stringResolved = stringWithVars;
    vars.forEach((varName) => {
      const varValue = this.evaluateVariable(varName, projectId, environmentId);
      stringResolved = stringResolved.replace(`{{${varName}}}`, varValue ?? '');
    });

    return stringResolved;
  }

  evaluateVariable(
    variableKey: string,
    projectId: string | undefined,
    environmentId: EnvironmentId,
  ): string | undefined {
    if (projectId != null) {
      const projectValue =
        this.#variableValues.projectScope[projectId]?.[variableKey]?.[environmentId]?.value;
      if (projectValue != null) {
        return projectValue;
      }

      const projectVariable = this.findProjectVariable(variableKey, projectId);
      const staticValue = projectVariable?.staticValues?.[environmentId];
      if (staticValue != null) {
        return staticValue;
      }
    }

    const globalValue = this.#variableValues.globalScope[variableKey]?.[environmentId]?.value;
    if (globalValue != null) {
      return globalValue;
    }

    const globalVariable = this.findGlobalVariable(variableKey);
    const staticValue = globalVariable?.staticValues?.[environmentId];
    if (staticValue != null) {
      return staticValue;
    }

    return undefined;
  }

  private findGlobalVariable(variableKey: string): Variable | undefined {
    return this.#variables.globalScope.find((v) => v.key === variableKey);
  }

  private findProjectVariable(variableKey: string, projectId: string): Variable | undefined {
    return this.#variables.projectScope[projectId]?.find((v) => v.key === variableKey);
  }

  async createProjectVariable(variable: Variable, project: Project) {
    if (this.#variables.projectScope[project.id]?.some((v) => v.key === variable.key)) {
      throw new Error(`Variable already exists on project '${project.name}'`);
    }

    if (variable.key.trim() === '') {
      throw new Error(`Variable needs to have a key`);
    }

    const projectVariables = this.#variables.projectScope[project.id] ?? [];
    projectVariables.push(variable);
    this.#variables.projectScope[project.id] = projectVariables;

    await this.saveVariables();
  }

  async updateProjectVariable(variable: Variable, project: Project) {
    const projectVariable = this.#variables.projectScope[project.id]?.find(
      (v) => v.key === variable.key,
    );

    if (projectVariable == null) {
      throw new Error(`Variable doesn't exists on project '${project.name}'`);
    }

    projectVariable.staticValues = variable.staticValues;

    await this.saveVariables();
  }

  async deleteProjectVariable(variableKey: string, project: Project) {
    const projectVariableIndex = this.#variables.projectScope[project.id]?.findIndex(
      (v) => v.key === variableKey,
    );

    if (
      this.#variables.projectScope[project.id] == null ||
      projectVariableIndex == null ||
      projectVariableIndex < 0
    ) {
      throw new Error(`Variable doesn't exist on project '${project.name}'`);
    }

    this.#variables.projectScope[project.id].splice(projectVariableIndex, 1);

    delete this.#variableValues.projectScope[project.id]?.[variableKey];

    const doesntAlsoExistInGlobalScope = this.findGlobalVariable(variableKey) == null;
    if (doesntAlsoExistInGlobalScope) {
      await this.projectsService.removeReferencesToVariable(variableKey, project);
    }

    await this.saveVariables();
  }

  async createGlobalVariable(variable: Variable) {
    if (this.#variables.globalScope.some((v) => v.key === variable.key)) {
      throw new Error('Variable already exists');
    }

    if (variable.key.trim() === '') {
      throw new Error(`Variable needs to have a key`);
    }

    this.#variables.globalScope.push(variable);

    await this.saveVariables();
  }

  async updateGlobalVariable(variable: Variable) {
    const globalVariable = this.#variables.globalScope.find((v) => v.key === variable.key);

    if (globalVariable == null) {
      throw new Error("Variable doesn't exists in global scope");
    }

    globalVariable.staticValues = variable.staticValues;

    await this.saveVariables();
  }

  async deleteGlobalVariable(variableKey: string) {
    const globalVariableIndex = this.#variables.globalScope.findIndex((v) => v.key === variableKey);

    if (
      this.#variables.globalScope == null ||
      globalVariableIndex == null ||
      globalVariableIndex < 0
    ) {
      throw new Error("Variable doesn't exist in global scope");
    }

    this.#variables.globalScope.splice(globalVariableIndex, 1);

    delete this.#variableValues.globalScope[variableKey];

    for (const project of this.projectsService.projects) {
      const doesntAlsoExistInProjectScope =
        this.findProjectVariable(variableKey, project.id) == null;
      if (doesntAlsoExistInProjectScope) {
        await this.projectsService.removeReferencesToVariable(variableKey, project);
      }
    }

    await this.saveVariables();
  }

  async setVariableValue(
    variableKey: string,
    projectId: string | undefined,
    environmentId: EnvironmentId,
    value: Omit<VariableValueSlot, 'timestamp'>,
  ) {
    const projectVariable = projectId
      ? this.findProjectVariable(variableKey, projectId)
      : undefined;
    if (projectVariable != null && projectId != null) {
      if (this.#variableValues.projectScope[projectId] == null) {
        this.#variableValues.projectScope[projectId] = {};
      }
      if (this.#variableValues.projectScope[projectId][variableKey] == null) {
        this.#variableValues.projectScope[projectId][variableKey] = {};
      }
      this.#variableValues.projectScope[projectId][variableKey][environmentId] = {
        ...value,
        timestamp: new Date(),
      };
    } else {
      const globalVariable = this.findGlobalVariable(variableKey);
      if (globalVariable != null) {
        if (this.#variableValues.globalScope[variableKey] == null) {
          this.#variableValues.globalScope[variableKey] = {};
        }
        this.#variableValues.globalScope[variableKey][environmentId] = {
          ...value,
          timestamp: new Date(),
        };
      } else {
        await this.messageService.error({
          title: 'Error setting variable',
          message: `Cannot set variable '${variableKey}' because it's not defined.`,
          detail: 'Please define it or un-set it from the response variables.',
        });
      }
    }

    await this.saveVariables();
  }

  async unsetVariableValue(
    variableKey: string,
    projectId: string,
    sourceId: string,
    sourceType: VariableValueSourceType,
  ) {
    const projectVariableEnvironmentsValue =
      this.#variableValues.projectScope[projectId]?.[variableKey];
    if (projectVariableEnvironmentsValue != null) {
      for (const environmentId in projectVariableEnvironmentsValue) {
        const variableValue = projectVariableEnvironmentsValue[environmentId];
        if (
          variableValue != null &&
          variableValue.sourceId === sourceId &&
          variableValue.sourceType === sourceType
        ) {
          delete projectVariableEnvironmentsValue[environmentId];
        }
      }
      if (Object.keys(projectVariableEnvironmentsValue).length === 0) {
        delete this.#variableValues.projectScope[projectId][variableKey];
      }
      await this.saveVariables();
    }

    const globalVariableEnvironmentsValue = this.#variableValues.globalScope[variableKey];
    if (globalVariableEnvironmentsValue != null) {
      for (const environmentId in globalVariableEnvironmentsValue) {
        const variableValue = globalVariableEnvironmentsValue[environmentId];
        if (
          variableValue != null &&
          variableValue.sourceId === sourceId &&
          variableValue.sourceType === sourceType
        ) {
          delete globalVariableEnvironmentsValue[environmentId];
        }
      }
      if (Object.keys(globalVariableEnvironmentsValue).length === 0) {
        delete this.#variableValues.globalScope[variableKey];
      }
      await this.saveVariables();
    }
  }

  private async saveVariables() {
    try {
      const variablesJson = JSON.stringify(this.#variables);
      await this.system.saveValue(VARIABLES, variablesJson);

      try {
        const variableValuesJson = JSON.stringify(this.#variableValues);
        await this.system.saveValue(VARIABLE_VALUES, variableValuesJson);
      } catch (e: any) {
        await this.messageService.error({
          title: 'Error saving variable values',
          message: 'The values set from the last action may not be saved',
          detail: e?.message,
        });
      }
    } catch (e: any) {
      await this.messageService.error({
        title: 'Error saving variables',
        message: 'The changes you made may not be saved',
        detail: e?.message,
      });
    }
  }
}

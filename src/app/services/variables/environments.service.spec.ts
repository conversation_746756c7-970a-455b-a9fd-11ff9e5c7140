import { TestBed } from '@angular/core/testing';
import { SystemService } from '../system/system.service';
import { UserMessageService } from '../system/user-message.service';
import { Environment } from '../../model/environment';
import { EnvironmentsService } from './environments.service';

describe('EnvironmentsServices', () => {
  let systemService: SystemService;
  let userMessageService: UserMessageService;
  let service: EnvironmentsService;

  async function setUp(
    testData: {
      environments?: Environment[];
    } = {},
  ) {
    systemService = jasmine.createSpyObj('SystemService', {
      ['loadValue']: Promise.resolve(JSON.stringify(testData.environments)),
      ['saveValue']: Promise.resolve(undefined),
    });
    userMessageService = jasmine.createSpyObj<UserMessageService>('UserMessageService', {
      ['confirm']: Promise.resolve(),
    });

    TestBed.configureTestingModule({
      providers: [
        EnvironmentsService,
        {
          provide: SystemService,
          useValue: systemService,
        },
        {
          provide: UserMessageService,
          useValue: userMessageService,
        },
      ],
    });
    service = TestBed.inject(EnvironmentsService);
    await service.initialise();
  }

  it('should sort the environments', async () => {
    await setUp({
      environments: [
        {
          id: 'env1',
          name: 'environment one',
        },
        {
          id: 'env2',
          name: 'environment two',
        },
        {
          id: 'env3',
          name: 'Another environment',
        },
      ],
    });
    expect(service.environments).toEqual([
      { id: 'env3', name: 'Another environment' },
      { id: 'env1', name: 'environment one' },
      { id: 'env2', name: 'environment two' },
    ]);
  });

  it('should set active environment', async () => {
    await setUp({
      environments: [
        {
          id: 'env1',
          name: 'environment one',
        },
        {
          id: 'env2',
          name: 'environment two',
        },
        {
          id: 'env3',
          name: 'Another environment',
        },
      ],
    });
    expect(service.activeEnvironment).toEqual({ id: 'env1', name: 'environment one' });

    await service.select({ id: 'env2', name: 'environment two' });
    expect(service.activeEnvironment).toEqual({ id: 'env2', name: 'environment two' });
  });
});

import { BackendService } from './backend.service';
import { ISystemService, UserMessage } from '../../../common/system';
import {
  HttpRequest,
  HttpResponse,
  HttpResponseHeader,
  IHttpClientService,
} from '../../../common/http-client';
import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { catchError, throwError } from 'rxjs';
import { MessageDialogComponent } from '../../ui/_common/message-dialog/message-dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Injectable()
export class WebBackendService implements BackendService {
  constructor(
    private readonly webSystemService: WebSystemService,
    private readonly webHttpClientService: WebHttpClientService,
  ) {
    console.warn('Using web backend, this is very limited!!!');
  }

  callAsync<T>(name: string, args: {} = {}): Promise<T> {
    switch (name) {
      case 'SystemService.ping':
        return this.webSystemService.ping();
      case 'SystemService.messageUser':
        return this.webSystemService.messageUser((<any>args).message as any).then((x) => x as any);
      case 'SystemService.loadValue':
        return this.webSystemService.loadValue((<any>args).name as string).then((x) => x as any);
      case 'SystemService.saveValue':
        return this.webSystemService
          .saveValue((<any>args).name as string, (<any>args).value as string)
          .then((x) => x as any);
      case 'HttpClientService.exchange':
        return this.webHttpClientService
          .exchange((<any>args).request as HttpRequest)
          .then((x) => x as any);
      default:
        return Promise.reject(new Error(`Unsupported call to ${name}.`));
    }
  }
}

@Injectable()
export class WebSystemService implements ISystemService {
  constructor(private readonly dialog: MatDialog) {}

  ping(): Promise<any> {
    return Promise.resolve({
      name: 'pong',
      async: true,
    });
  }

  messageUser(message: UserMessage): Promise<boolean> {
    return new Promise<boolean>((resolve, _) => {
      const dialogRef = this.dialog.open(MessageDialogComponent, {
        data: message,
      });

      dialogRef.afterClosed().subscribe((result: boolean | undefined) => {
        resolve(result ?? false);
      });
    });
  }

  loadValue(name: string): Promise<string | undefined> {
    const stringValue = localStorage.getItem(name);
    if (stringValue == null || stringValue.trim().length === 0) {
      return Promise.resolve(undefined);
    } else {
      return Promise.resolve(stringValue);
    }
  }

  saveValue(name: string, jsonValue: string): Promise<void> {
    localStorage.setItem(name, jsonValue);
    return Promise.resolve();
  }
}

@Injectable()
export class WebHttpClientService implements IHttpClientService {
  constructor(private readonly http: HttpClient) {
    console.warn(
      'Using the browser capabilities for making HTTP calls, this can be limited by CORS.',
    );
  }

  exchange(request: HttpRequest): Promise<any> {
    return new Promise<HttpResponse | undefined>((resolve, reject) => {
      const startTimeMS = new Date().getTime();
      return this.callApi(request)
        .pipe(
          catchError((error: HttpErrorResponse) => {
            if (error.status === 0) {
              reject(new Error(error.error));
            } else {
              resolve(
                this.withRequestInfo(startTimeMS, {
                  status: error.status,
                  payload: error.error,
                  headers: this.headersOf(error.headers),
                }),
              );
            }
            return throwError(() => new Error(`HTTP call failed (status: ${error.status}).`));
          }),
        )
        .subscribe((response) => {
          resolve(
            this.withRequestInfo(startTimeMS, {
              status: response.status,
              payload: response.body,
              headers: this.headersOf(response.headers),
            }),
          );
        });
    });
  }

  private callApi(request: HttpRequest) {
    const requestHeaders: { [header: string]: string | string[] } = Object.entries(
      request.headers ?? {},
    )
      .filter(([, value]) => value != null)
      .reduce((acc, [name, value]) => {
        const object = { ...acc };
        object[name] = value;
        return object;
      }, {} as any);
    const protocol = request.https ? 'https://' : 'http://';
    const port = request.port ? `:${request.port}` : '';
    const url = `${protocol}${request.hostname}${port}${request.path}${request.query ?? ''}${
      request.hash ?? ''
    }`;

    return this.http.request(request.method, url, {
      body: request.payload,
      headers: requestHeaders,
      responseType: 'text',
      observe: 'response' as const,
    });
  }

  private withRequestInfo(startTimeMS: number, response: HttpResponse): HttpResponse {
    const endTimeMS = new Date().getTime();
    console.debug('Request took (ms)', endTimeMS - startTimeMS);
    return {
      ...response,
      info: {
        initiatedAt: startTimeMS,
        completedAt: endTimeMS,
      },
    };
  }

  private headersOf(headers: HttpHeaders): HttpResponseHeader[] {
    return headers.keys().map((key) => {
      const values = headers.getAll(key);
      return {
        name: key,
        values,
      } as HttpResponseHeader;
    });
  }
}

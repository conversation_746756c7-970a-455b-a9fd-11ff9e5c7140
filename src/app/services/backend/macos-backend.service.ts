import { filter, Subject, Subscription } from 'rxjs';
import { BackendService } from './backend.service';
import { v4 as uuidv4 } from 'uuid';

export class MacOSBackendService implements BackendService {
  private readonly responseSubject = new Subject();

  constructor() {
    if (!MacOSBackendService.isSupported()) {
      console.error('Using MacOS backend, but that is not supported!');
      return;
    }

    const responseSubject = this.responseSubject;
    (<any>window).headrestMacosCallAsyncResponse = function (
      name: string,
      callId: string,
      response: {},
    ) {
      responseSubject.next({
        name,
        callId,
        response,
      });
    };
  }

  static isSupported(): boolean {
    return typeof (<any>window).headrestMacosVersion !== 'undefined';
  }

  callAsync<T>(name: string, args: {} = {}): Promise<T> {
    const callId = uuidv4();
    return new Promise<T>((resolve, reject) => {
      console.debug('Sending message', name);

      let subscription: Subscription | undefined = undefined;
      subscription = this.responseSubject
        .asObservable()
        .pipe(
          filter((value) => {
            const messageResponse = value as MessageResponse | undefined;
            if (messageResponse == null) {
              return false;
            }
            return messageResponse.name === name && messageResponse.callId === callId;
          }),
        )
        .subscribe((value) => {
          const messageResponse = value as MessageResponse;
          if (messageResponse.response?._error != null) {
            console.debug(`Call to ${name}(${args}) failed`, messageResponse.response._error);
            reject(new Error(messageResponse.response._error));
          } else {
            console.debug(`Call to ${name}(${args}) returns`, messageResponse.response);
            resolve(messageResponse.response);
          }
          subscription?.unsubscribe();
        });

      (<any>window).webkit.messageHandlers.headrestMacosCallAsync.postMessage({
        name,
        callId,
        args,
      });
    });
  }
}

type MessageResponse = {
  name: string;
  callId: string;
  response: any | undefined;
};

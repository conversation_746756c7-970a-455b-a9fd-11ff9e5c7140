import { BackendService } from './backend.service';
import { <PERSON>p<PERSON><PERSON><PERSON><PERSON> } from 'electron';
import { v4 as uuidv4 } from 'uuid';

export class ElectronBackendService implements BackendService {
  private readonly ipc?: IpcRenderer;

  constructor() {
    if (ElectronBackendService.isSupported()) {
      this.ipc = (<any>window).require('electron').ipcRenderer;
    } else {
      console.error('Using Electron backend, but that is not supported!');
    }
  }

  static isSupported(): boolean {
    return typeof window.require !== 'undefined' && (<any>window).require('electron') != null;
  }

  callAsync<T>(name: string, args: {} = {}): Promise<T> {
    const callId = uuidv4();
    return new Promise<T>((resolve, reject) => {
      if (this.ipc == null) {
        const errorMessage = 'No Electron IPC in this environment';
        reject(new Error(errorMessage));
      } else {
        this.ipc.once(`${name}.response.${callId}`, (event, response) => {
          if (response?._error != null) {
            console.debug(`Call to ${name}(${args}) failed`, response._error);
            reject(new Error(response._error));
          } else {
            console.debug(`Call to ${name}(${args}) returns`, response);
            resolve(response);
          }
        });
        console.debug('Sending message', name);
        this.ipc.send(name, callId, args);
      }
    });
  }
}

import { Injectable } from '@angular/core';
import {
  Project,
  ProjectFolder,
  ProjectItem,
  ProjectItemFolder,
  ProjectItemRequest,
  ProjectItemType,
} from '../../model/project';
import { SystemService } from '../system/system.service';
import {
  createDefaultRequest,
  createProjectFolderId,
  createProjectId,
  Request,
} from '../../model/request';
import { UserMessageService } from '../system/user-message.service';

const PROJECTS = 'projects';
export const SCRATCHES_PROJECT_ID = 'project-scratches';

@Injectable()
export class ProjectsService {
  #projects: Project[] = [];

  constructor(
    private readonly system: SystemService,
    private readonly messageService: UserMessageService,
  ) {}

  async initialise() {
    await this.loadProjects();
  }

  get projects(): Project[] {
    return this.#projects;
  }

  async loadProjects() {
    return new Promise<void>((resolve) => {
      this.system.loadValue(PROJECTS).then((projectsJson) => {
        if (projectsJson == null) {
          this.initialiseProjects();
        } else {
          try {
            const projects = JSON.parse(projectsJson);
            this.#projects = projects as Project[];
          } catch (e) {
            console.error(`Error parsing projects value after loading them from system`, e);
            this.initialiseProjects();
          }
        }
        resolve();
      });
    });
  }

  async saveProjects() {
    console.debug('Saving projects');
    try {
      const projectsJson = JSON.stringify(this.#projects);
      await this.system.saveValue(PROJECTS, projectsJson);
    } catch (e: any) {
      await this.messageService.error({
        title: 'Error saving project data',
        message: 'The changes you made may not be saved',
        detail: e?.message,
      });
    }
  }

  private initialiseProjects() {
    this.#projects = [
      {
        ...this.createScratchesProject(),
        items: [
          {
            type: ProjectItemType.REQUEST,
            value: createDefaultRequest(),
          },
        ],
      },
    ];
  }

  private createScratchesProject(): Project {
    return {
      id: SCRATCHES_PROJECT_ID,
      name: 'Scratches',
      items: [],
    };
  }

  async createScratchRequest(): Promise<Request> {
    let scratchesProject = this.findById(SCRATCHES_PROJECT_ID)?.item as Project;
    if (scratchesProject == null) {
      scratchesProject = this.createScratchesProject();
      this.#projects.unshift(scratchesProject);
    }
    const projectItemRequest = {
      type: ProjectItemType.REQUEST,
      value: createDefaultRequest(),
    };
    scratchesProject.items.unshift(projectItemRequest);

    await this.saveProjects();
    await this.loadProjects();

    return projectItemRequest.value;
  }

  async createRequest(parent: Project | ProjectItemFolder): Promise<Request | undefined> {
    const items = this.itemsOf(parent);
    if (items == null) {
      return undefined;
    }

    const newRequest = createDefaultRequest();
    items.unshift({
      type: ProjectItemType.REQUEST,
      value: newRequest,
    } as ProjectItemRequest);

    await this.saveProjects();
    return newRequest;
  }

  async createProjectFolder(
    parent: Project | ProjectItemFolder,
  ): Promise<ProjectFolder | undefined> {
    const items = this.itemsOf(parent);
    if (items == null) {
      return undefined;
    }

    const newFolder: ProjectFolder = {
      id: createProjectFolderId(),
      name: 'New folder',
      items: [],
    };
    items.unshift({
      type: ProjectItemType.FOLDER,
      value: newFolder,
    } as ProjectItemFolder);

    await this.saveProjects();
    return newFolder;
  }

  async createProject(): Promise<Project | undefined> {
    const newProject: Project = {
      id: createProjectId(),
      name: 'New project',
      items: [],
    };
    if (this.projects.length > 0) {
      this.projects.splice(1, 0, newProject);
    } else {
      this.projects.unshift(newProject);
    }

    await this.saveProjects();
    return newProject;
  }

  async move(
    source: Project | ProjectItem,
    newIndex: number,
    newParent: Project | ProjectItemFolder | undefined,
  ) {
    const sourceId = this.idOf(source);
    const sourceParent = this.findById(sourceId)?.parent;

    const itemsOfParent = this.itemsOf(sourceParent);
    const indexOfSource = itemsOfParent.findIndex((item) => this.idOf(item) === sourceId);
    itemsOfParent.splice(indexOfSource, 1);

    const destId = this.idOf(newParent);
    const destParent = this.findById(destId)?.item;

    this.itemsOf(destParent).splice(newIndex, 0, source);

    await this.saveProjects();
    await this.loadProjects();
  }

  findById(id: string | undefined): ItemWithParent | undefined {
    if (id == null) {
      return undefined;
    }
    return this.find((itemOrProject: any) => this.idOf(itemOrProject) === id);
  }

  allIds(): string[] {
    const idsOf = (parent?: Project | ProjectItem) => {
      let ids = new Array<string>();
      const parentId = this.idOf(parent);
      if (parentId != null) {
        ids.push(parentId);
      }
      const children = this.itemsOf(parent);
      for (const child of children) {
        ids.push(...idsOf(child));
      }
      return ids;
    };

    return idsOf();
  }

  private find(
    matcher: (item: Project | ProjectItem) => boolean,
    parent?: Project | ProjectItemFolder,
  ): ItemWithParent | undefined {
    const childItems = this.itemsOf(parent);

    for (const childItem of childItems) {
      if (matcher(childItem)) {
        return {
          item: childItem,
          parent,
        };
      }
      if ((childItem as Project).items ?? (childItem as ProjectItemFolder).value.items != null) {
        const childParent = this.find(matcher, childItem as Project | ProjectItemFolder);
        if (childParent != null) {
          return childParent;
        }
      }
    }
    return undefined;
  }

  findAll(
    matcher: (item: Project | ProjectItem) => boolean,
    parent?: Project | ProjectItem,
  ): Array<Project | ProjectItem> {
    let allItems: Array<Project | ProjectItem> = [];
    const childItems = this.itemsOf(parent);

    for (const childItem of childItems) {
      if (matcher(childItem)) {
        allItems.push(childItem);
      }

      if (this.itemsOf(childItem).length > 0) {
        const matchingDescendants = this.findAll(matcher, childItem);
        allItems.push(...matchingDescendants);
      }
    }
    return allItems;
  }

  findProjectOf(id: string): Project | undefined {
    return this.projects.find((project) => {
      return this.find((itemOrProject: any) => this.idOf(itemOrProject) === id, project) != null;
    });
  }

  private idOf(item: Project | ProjectItem | undefined): string | undefined {
    return (item as Project)?.id ?? (item as ProjectItem)?.value?.id;
  }

  private itemsOf(item: Project | ProjectItem | undefined): Array<Project | ProjectItem> {
    if (item == null) {
      return this.#projects;
    }
    return (item as Project).items ?? (item as ProjectItemFolder).value?.items ?? [];
  }

  async rename(item: Project | ProjectItem, newName: string) {
    const itemId = this.idOf(item);

    if (itemId === SCRATCHES_PROJECT_ID) {
      console.error('Cannot rename the Scratches project');
      return;
    }

    const toRename = this.findById(itemId)?.item;

    if ((toRename as Project)?.id != null) {
      (toRename as Project).name = newName;
    } else if ((toRename as ProjectItem)?.value != null) {
      (toRename as ProjectItem).value.name = newName;
    }

    await this.saveProjects();
    await this.loadProjects();
  }

  async delete(item: Project | ProjectItem) {
    const itemId = this.idOf(item);
    if (itemId === SCRATCHES_PROJECT_ID) {
      console.error('Cannot delete the Scratches project');
      return;
    }
    const toDelete: ItemWithParent | undefined = this.findById(itemId);
    if (toDelete == null) {
      return;
    }
    const items = this.itemsOf(toDelete.parent);
    const indexToRemove = items.findIndex((i) => this.idOf(i) === itemId);
    items.splice(indexToRemove, 1);

    await this.saveProjects();
    await this.loadProjects();
  }

  async removeReferencesToVariable(variableKey: string, project: Project) {
    this.findAll(
      (item) => (item as ProjectItemRequest)?.value?.setVariables != null,
      project,
    ).forEach((item) => {
      const requestItem = item as ProjectItemRequest;
      requestItem.value.setVariables = requestItem.value.setVariables?.filter(
        (setVariable) => setVariable.variableKey != variableKey,
      );
    });

    await this.saveProjects();
    await this.loadProjects();
  }
}

type ItemWithParent = {
  item: Project | ProjectItem;
  parent: Project | ProjectItemFolder | undefined;
};

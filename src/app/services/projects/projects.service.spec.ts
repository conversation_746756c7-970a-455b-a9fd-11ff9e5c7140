import { TestBed } from '@angular/core/testing';
import { ProjectsService } from './projects.service';
import { SystemService } from '../system/system.service';
import { Project, ProjectFolder, ProjectItemType } from '../../model/project';
import {
  PayloadType,
  RawPayloadType,
  RequestMethod,
  RequestPayload,
  Request,
} from '../../model/request';
import { UserMessageService } from '../system/user-message.service';

describe('ProjectsService', () => {
  let systemService: SystemService;
  let userMessageService: UserMessageService;
  let service: ProjectsService;

  beforeEach(async () => {
    systemService = jasmine.createSpyObj('SystemService', {
      ['loadValue']: Promise.resolve(JSON.stringify([projectA, projectB])),
      ['saveValue']: Promise.resolve(),
    });
    userMessageService = jasmine.createSpyObj<UserMessageService>('UserMessageService', {
      ['confirm']: Promise.resolve(),
    });
    TestBed.configureTestingModule({
      providers: [
        ProjectsService,
        {
          provide: SystemService,
          useValue: systemService,
        },
        {
          provide: UserMessageService,
          useValue: userMessageService,
        },
      ],
    });
    service = TestBed.inject(ProjectsService);
  });

  it('should load the projects', async () => {
    await service.loadProjects();
    console.debug(JSON.stringify(service.projects));
    expect(service.projects).toEqual([projectA, projectB]);
  });

  describe('Find by id', () => {
    beforeEach(async () => {
      await service.loadProjects();
    });

    it('should find item and parent project by id', async () => {
      expect(service.findById(requestB2.id)).toEqual({
        item: {
          type: ProjectItemType.REQUEST,
          value: requestB2,
        },
        parent: projectB,
      });
    });

    it('should find project without parent by id', async () => {
      expect(service.findById(projectB.id)).toEqual({
        item: projectB,
        parent: undefined,
      });
    });

    it('should find folder with project parent by id', async () => {
      expect(service.findById(folderA2.id)).toEqual({
        item: {
          type: ProjectItemType.FOLDER,
          value: folderA2,
        },
        parent: projectA,
      });
    });

    it('should find request session with folder parent by id', async () => {
      const result = service.findById(requestA2.id);
      expect(result).toEqual({
        item: {
          type: ProjectItemType.REQUEST,
          value: requestA2,
        },
        parent: {
          type: ProjectItemType.FOLDER,
          value: folderA1,
        },
      });
    });

    it('should find nothing by wrong id', async () => {
      expect(service.findById('wrong')).toBeUndefined();
    });

    it('should get all ids', async () => {
      expect(service.allIds()).toEqual([
        projectA.id,
        folderA1.id,
        requestA1.id,
        requestA2.id,
        requestA3.id,
        requestA4.id,
        folderA2.id,
        projectB.id,
        requestB1.id,
        requestB2.id,
        requestB3.id,
      ]);
    });
  });

  describe('Move', () => {
    beforeEach(async () => {
      await service.loadProjects();
    });

    it('should move request session within project', async () => {
      await service.move(
        {
          type: ProjectItemType.REQUEST,
          value: requestB3,
        },
        0,
        projectB,
      );
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          projectA,
          {
            ...projectB,
            items: [
              {
                type: ProjectItemType.REQUEST,
                value: requestB3,
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestB1,
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestB2,
              },
            ],
          },
        ]),
      );
    });

    it('should reorder projects (move at root)', async () => {
      await service.move(projectA, 1, undefined);
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([projectB, projectA]),
      );
    });

    it('should move request session to another project and folder', async () => {
      await service.move({ type: ProjectItemType.REQUEST, value: requestB1 }, 1, {
        type: ProjectItemType.FOLDER,
        value: folderA1,
      });
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          {
            ...projectA,
            items: [
              {
                type: ProjectItemType.FOLDER,
                value: {
                  ...folderA1,
                  items: [
                    {
                      type: ProjectItemType.REQUEST,
                      value: requestA1,
                    },
                    {
                      type: ProjectItemType.REQUEST,
                      value: requestB1,
                    },
                    {
                      type: ProjectItemType.REQUEST,
                      value: requestA2,
                    },
                    {
                      type: ProjectItemType.REQUEST,
                      value: requestA3,
                    },
                  ],
                },
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestA4,
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA2,
              },
            ],
          },
          {
            ...projectB,
            items: [
              {
                type: ProjectItemType.REQUEST,
                value: requestB2,
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestB3,
              },
            ],
          },
        ]),
      );
    });

    it('should move an entire folder to another project', async () => {
      await service.move({ type: ProjectItemType.FOLDER, value: folderA1 }, 3, projectB);
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          {
            ...projectA,
            items: [
              {
                type: ProjectItemType.REQUEST,
                value: requestA4,
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA2,
              },
            ],
          },
          {
            ...projectB,
            items: [
              {
                type: ProjectItemType.REQUEST,
                value: requestB1,
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestB2,
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestB3,
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA1,
              },
            ],
          },
        ]),
      );
    });
  });

  describe('Rename', () => {
    beforeEach(async () => {
      await service.loadProjects();
    });

    it('should rename request session within project', async () => {
      await service.rename(
        {
          type: ProjectItemType.REQUEST,
          value: requestA4,
        },
        'Name name A4',
      );
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          {
            ...projectA,
            items: [
              {
                type: ProjectItemType.FOLDER,
                value: folderA1,
              },
              {
                type: ProjectItemType.REQUEST,
                value: { ...requestA4, name: 'Name name A4' },
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA2,
              },
            ],
          },
          projectB,
        ]),
      );
    });

    it('should rename folder within project', async () => {
      await service.rename(
        {
          type: ProjectItemType.FOLDER,
          value: folderA1,
        },
        'Name name folder A1',
      );
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          {
            ...projectA,
            items: [
              {
                type: ProjectItemType.FOLDER,
                value: { ...folderA1, name: 'Name name folder A1' },
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestA4,
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA2,
              },
            ],
          },
          projectB,
        ]),
      );
    });

    it('should rename request session within folder', async () => {
      await service.rename(
        {
          type: ProjectItemType.REQUEST,
          value: requestA1,
        },
        'Name name request A1',
      );
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          {
            ...projectA,
            items: [
              {
                type: ProjectItemType.FOLDER,
                value: {
                  ...folderA1,
                  items: [
                    {
                      type: ProjectItemType.REQUEST,
                      value: {
                        ...requestA1,
                        name: 'Name name request A1',
                      },
                    },
                    {
                      type: ProjectItemType.REQUEST,
                      value: requestA2,
                    },
                    {
                      type: ProjectItemType.REQUEST,
                      value: requestA3,
                    },
                  ],
                },
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestA4,
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA2,
              },
            ],
          },
          projectB,
        ]),
      );
    });
  });

  describe('Delete', () => {
    beforeEach(async () => {
      await service.loadProjects();
    });

    it('should delete request session within project', async () => {
      await service.delete({
        type: ProjectItemType.REQUEST,
        value: requestA4,
      });
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          {
            ...projectA,
            items: [
              {
                type: ProjectItemType.FOLDER,
                value: folderA1,
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA2,
              },
            ],
          },
          projectB,
        ]),
      );
    });

    it('should delete request session within folder', async () => {
      await service.delete({
        type: ProjectItemType.REQUEST,
        value: requestA1,
      });
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          {
            ...projectA,
            items: [
              {
                type: ProjectItemType.FOLDER,
                value: {
                  ...folderA1,
                  items: [
                    {
                      type: ProjectItemType.REQUEST,
                      value: requestA2,
                    },
                    {
                      type: ProjectItemType.REQUEST,
                      value: requestA3,
                    },
                  ],
                },
              },
              {
                type: ProjectItemType.REQUEST,
                value: requestA4,
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA2,
              },
            ],
          },
          projectB,
        ]),
      );
    });

    it('should delete project', async () => {
      await service.delete(projectB);
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([projectA]),
      );
    });

    it('should delete folder', async () => {
      await service.delete({ type: ProjectItemType.FOLDER, value: folderA1 });
      expect(systemService.saveValue).toHaveBeenCalledOnceWith(
        'projects',
        JSON.stringify([
          {
            ...projectA,
            items: [
              {
                type: ProjectItemType.REQUEST,
                value: requestA4,
              },
              {
                type: ProjectItemType.FOLDER,
                value: folderA2,
              },
            ],
          },
          projectB,
        ]),
      );
    });
  });
});

const request = {
  method: RequestMethod.GET,
  headers: [],
  queryParams: [],
  payload: {
    type: PayloadType.NONE,
    rawPayload: {
      type: RawPayloadType.JSON,
      body: '',
    },
    formPayload: {
      params: [],
    },
  } as RequestPayload,
};

const requestA1: Request = {
  ...request,
  id: 'rs-a-1',
  name: 'Request A 1',
  url: 'https://request/a/1',
};

const requestA2: Request = {
  ...request,
  id: 'rs-a-2',
  name: 'Request A 2',
  url: 'https://request/a/2',
};

const requestA3: Request = {
  ...request,
  id: 'rs-a-3',
  name: 'Request A 3',
  url: 'https://request/a/3',
};

const requestA4: Request = {
  ...request,
  id: 'rs-a-4',
  name: 'Request A 4',
  url: 'https://request/a/4',
};

const folderA1: ProjectFolder = {
  id: 'folder-a-1',
  name: 'Folder A 1',
  items: [
    {
      type: ProjectItemType.REQUEST,
      value: requestA1,
    },
    {
      type: ProjectItemType.REQUEST,
      value: requestA2,
    },
    {
      type: ProjectItemType.REQUEST,
      value: requestA3,
    },
  ],
};

const folderA2: ProjectFolder = {
  id: 'folder-a-2',
  name: 'Folder A 2',
  items: [],
};

const projectA: Project = {
  id: 'project-a',
  name: 'Project A',
  items: [
    {
      type: ProjectItemType.FOLDER,
      value: folderA1,
    },
    {
      type: ProjectItemType.REQUEST,
      value: requestA4,
    },
    {
      type: ProjectItemType.FOLDER,
      value: folderA2,
    },
  ],
};

const requestB1: Request = {
  ...request,
  id: 'rs-b-1',
  name: 'Request B 1',
  url: 'https://request/b/1',
};

const requestB2: Request = {
  ...request,
  id: 'rs-b-2',
  name: 'Request B 2',
  url: 'https://request/b/2',
};

const requestB3: Request = {
  ...request,
  id: 'rs-b-3',
  name: 'Request B 3',
  url: 'https://request/b/3',
};

const projectB: Project = {
  id: 'project-b',
  name: 'Project B',
  items: [
    {
      type: ProjectItemType.REQUEST,
      value: requestB1,
    },
    {
      type: ProjectItemType.REQUEST,
      value: requestB2,
    },
    {
      type: ProjectItemType.REQUEST,
      value: requestB3,
    },
  ],
};

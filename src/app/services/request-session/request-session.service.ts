import { Injectable } from '@angular/core';
import { HttpClientService } from '../http/http-client.service';
import {
  createDefaultRequest,
  PayloadType,
  Request,
  RequestHeader,
  RequestQueryParam,
  SetVariable,
} from '../../model/request';
import { ProjectsService } from '../projects/projects.service';
import { UserMessageService } from '../system/user-message.service';
import { HttpResponse } from '../../../common/http-client';
import { ProjectItemRequest } from '../../model/project';
import { BehaviorSubject } from 'rxjs';
import { VariablesService } from '../variables/variables.service';
import { JSONPath } from 'jsonpath-plus';
import { VariableValueSourceType } from '../../model/variable';
import { ImperfectUrl } from '../http/imperfect-url';
import { EnvironmentsService } from '../variables/environments.service';

@Injectable()
export class RequestSessionService {
  request: Request = createDefaultRequest();
  requestInProgress = false;

  readonly #requestUpdatedSubject = new BehaviorSubject<Request>(this.request);
  readonly requestUpdated = this.#requestUpdatedSubject.asObservable();

  constructor(
    private readonly http: HttpClientService,
    private readonly messageService: UserMessageService,
    private readonly projects: ProjectsService,
    private readonly variablesService: VariablesService,
    private readonly environmentsService: EnvironmentsService,
  ) {}

  get project() {
    return this.projects.findProjectOf(this.request.id);
  }

  select(request: Request) {
    this.request = request;
    this.requestInProgress = false;
  }

  get url(): string {
    return this.request.url;
  }

  set url(value: string) {
    if (this.url != value) {
      this.request.url = value;
      this.urlUpdated(value);
      void this.saveRequest();
    }
  }

  private urlUpdated(newUrl: string) {
    const isActionable: (param: RequestQueryParam) => boolean = (queryParam) => {
      return !queryParam.disabled && (queryParam.name.trim() !== '' || queryParam.value != null);
    };
    const newQueryParams = new ImperfectUrl(newUrl).extractQueryParamsFromUrl();
    const existingQueryParams = this.request.queryParams.filter((param) => isActionable(param));
    if (this.asQueryString(newQueryParams) !== this.asQueryString(existingQueryParams)) {
      const existingUnActionableQueryParams = this.request.queryParams.filter(
        (param) => !isActionable(param),
      );

      this.request.queryParams = newQueryParams.concat(existingUnActionableQueryParams);
    }
  }

  private asQueryString(queryParams: RequestQueryParam[]): string {
    return queryParams
      .filter(
        (queryParam) =>
          !queryParam.disabled && (queryParam.name.trim() !== '' || queryParam.value != null),
      )
      .map((param) => `${param.name}=${param.value ?? ''}`)
      .join('&');
  }

  addHeader(name?: string, value?: string) {
    this.request.headers.push({
      name: name ?? '',
      value,
    });
    void this.saveRequest();
  }

  deleteHeaderAt(index: number) {
    this.request.headers.splice(index, 1);
    void this.saveRequest();
  }

  disableHeaderAt(index: number) {
    this.request.headers[index].disabled = true;
    void this.saveRequest();
  }

  enableHeaderAt(index: number) {
    this.request.headers[index].disabled = false;
    void this.saveRequest();
  }

  setContentType(contentType?: string) {
    const existingHeaderPredicate = (header: RequestHeader) =>
      header.name.toLowerCase() === 'content-type';

    const existingHeaderIndex = this.request.headers.findIndex(existingHeaderPredicate);
    if (contentType == null) {
      if (existingHeaderIndex >= 0) {
        this.deleteHeaderAt(existingHeaderIndex);
      }
    } else {
      const existingContentTypeHeader = this.request.headers[existingHeaderIndex];
      if (existingContentTypeHeader != null) {
        this.updateHeaderAt(existingHeaderIndex, contentType);
      } else {
        this.addHeader('Content-Type', contentType);
      }
    }
  }

  private updateHeaderAt(index: number, value?: string) {
    this.request.headers[index].value = value;
    void this.saveRequest();
  }

  addFormParam() {
    this.request.payload.formPayload.params.push({
      name: '',
    });
    void this.saveRequest();
  }

  deleteFormParamAt(index: number) {
    this.request.payload.formPayload.params.splice(index, 1);
    void this.saveRequest();
  }

  disableFormParamAt(index: number) {
    this.request.payload.formPayload.params[index].disabled = true;
    void this.saveRequest();
  }

  enableFormParamAt(index: number) {
    this.request.payload.formPayload.params[index].disabled = false;
    void this.saveRequest();
  }

  formPayload(): string {
    const payload = this.request.payload;
    if (payload.type === PayloadType.FORM) {
      const formPayload = payload.formPayload!;
      const params: string[][] = formPayload.params
        .filter((param) => !param.disabled)
        .map((param) => {
          const name = this.variablesService.evaluateStringWithVariables(
            param.name,
            this.project?.id,
            this.environmentsService.activeEnvironment.id,
          );
          const value = this.variablesService.evaluateStringWithVariables(
            param.value ?? '',
            this.project?.id,
            this.environmentsService.activeEnvironment.id,
          );
          return { name, value };
        })
        .filter((param) => param.name !== '' || param.value !== '')
        .map((param) => [param.name, param.value])
        .reduce(
          (previousValue, currentValue) => [...previousValue, currentValue],
          [] as string[][],
        );
      return new URLSearchParams(params).toString();
    } else {
      return '';
    }
  }

  private get requestPayload(): string {
    switch (this.request.payload.type) {
      case PayloadType.NONE:
        return '';
      case PayloadType.RAW:
        return this.variablesService.evaluateStringWithVariables(
          this.request.payload.rawPayload!.body,
          this.project?.id,
          this.environmentsService.activeEnvironment.id,
        );
      case PayloadType.FORM:
        return this.formPayload();
    }
  }

  async addSetVariable(setVariable: SetVariable) {
    const allVariablesInScope = [
      ...this.variablesService.variablesInGlobalScope(),
      ...this.variablesService.variablesInProjectScope(this.project),
    ];
    const inScope = allVariablesInScope.some((v) => v.key === setVariable.variableKey);
    if (!inScope) {
      await this.messageService.error({
        title: 'Error setting variable',
        message: 'Variable with this name is not defined in any scope.',
        detail: 'Please pick a new variable name.',
      });
      return;
    }

    if (this.request.setVariables == null) {
      this.request.setVariables = [];
    }

    const existingSetVariable = this.request.setVariables.find(
      (v) => v.variableKey === setVariable.variableKey,
    );
    if (existingSetVariable != null) {
      await this.messageService.error({
        title: 'Error setting variable',
        message: 'Variable with this key is already set.',
        detail: 'Please pick a new variable key.',
      });
      return;
    }

    this.request.setVariables.push(setVariable);
    await this.applyVariable(setVariable);
    void this.saveRequest();
  }

  async updateSetVariable(setVariable: SetVariable) {
    if (this.request.setVariables == null) {
      this.request.setVariables = [];
    }
    const existingSetVariable = this.request.setVariables.find(
      (v) => v.variableKey === setVariable.variableKey,
    );
    if (existingSetVariable == null) {
      await this.messageService.error({
        title: 'Error setting variable',
        message: 'Variable not found',
        detail: 'Please choose an existing variable to update',
      });
    } else {
      existingSetVariable.jsonPath = setVariable.jsonPath;
      await this.applyVariable(setVariable);
      void this.saveRequest();
    }
  }

  async deleteSetVariable(variableKey: string) {
    if (this.request.setVariables && this.project) {
      const existingSetVariableIndex: number = this.request.setVariables.findIndex(
        (v) => v.variableKey === variableKey,
      );
      if (existingSetVariableIndex >= 0) {
        this.request.setVariables.splice(existingSetVariableIndex, 1);
        await this.variablesService.unsetVariableValue(
          variableKey,
          this.project.id,
          this.request.id,
          VariableValueSourceType.REQUEST_SESSION,
        );
        void this.saveRequest();
      }
    }
  }

  get readyToSendRequest(): boolean {
    if (this.requestInProgress) {
      return false;
    }
    return this.url?.trim()?.length > 0;
  }

  async performRequest(): Promise<void> {
    if (this.requestInProgress) {
      return;
    }

    this.url = this.url?.trim();
    let urlString = this.variablesService.evaluateStringWithVariables(
      this.url,
      this.project?.id,
      this.environmentsService.activeEnvironment.id,
    );

    if (!urlString.startsWith('http')) {
      urlString = `http://${urlString}`;
    }

    let url: URL;
    try {
      url = new URL(urlString);
    } catch (e: any) {
      return this.messageService.error({
        title: 'Error performing the request',
        message: 'The URL is not valid',
        detail: urlString,
      });
    }

    const headers = this.request.headers
      .filter((h) => !h.disabled && h.value != null)
      .reduce((acc, crt) => {
        const object = { ...acc };
        const headerName = crt.name
          ? this.variablesService.evaluateStringWithVariables(
              crt.name,
              this.project?.id,
              this.environmentsService.activeEnvironment.id,
            )
          : undefined;
        const headerValue =
          crt.value != null
            ? this.variablesService.evaluateStringWithVariables(
                crt.value,
                this.project?.id,
                this.environmentsService.activeEnvironment.id,
              )
            : undefined;
        if (headerName != null) {
          object[headerName] = headerValue;
        }
        return object;
      }, {} as any);

    const payload = this.requestPayload || undefined;

    this.requestInProgress = true;

    const originatingRequestId = this.request.id;
    try {
      const response = await this.http.exchange({
        https: url.protocol.startsWith('https'),
        method: this.request.method,
        hostname: url.hostname,
        port: url.port || undefined,
        path: url.pathname ?? '',
        query: url.search || undefined,
        hash: url.hash || undefined,
        headers,
        payload,
      });
      await this.setResponse(originatingRequestId, response);
    } catch (e: any) {
      await this.setResponseError(originatingRequestId, e);
    }

    await this.saveRequest();
  }

  private async setResponse(originatingRequestId: string, response: HttpResponse) {
    let request = this.request;
    if (this.request.id != originatingRequestId) {
      console.debug(
        'Request id has changed before the response was returned. Old/new',
        originatingRequestId,
        this.request.id,
      );
      const projectItemRequest = this.projects.findById(originatingRequestId)
        ?.item as ProjectItemRequest;
      const requestToUpdate = projectItemRequest?.value;
      if (requestToUpdate != null) {
        request = requestToUpdate;
      } else {
        return console.warn(
          'Request with id cannot be found in any project. Aborting updating its response.',
          originatingRequestId,
        );
      }
    }

    request.response = {
      ...response,
      payload: this.formatAsJson(response.payload),
    };

    for (let setVariable of request.setVariables ?? []) {
      await this.applyVariableFromResponse(setVariable, response, request);
    }

    this.requestInProgress = false;
  }

  private async applyVariableFromResponse(
    setVariable: SetVariable,
    response: HttpResponse,
    request: Request,
  ) {
    const value = this.applyJsonPathToResponse(setVariable, response);
    console.debug('Setting variable from response', setVariable.variableKey);
    await this.variablesService.setVariableValue(
      setVariable.variableKey,
      this.project?.id,
      this.environmentsService.activeEnvironment.id,
      {
        value,
        sourceId: request.id,
        sourceType: VariableValueSourceType.REQUEST_SESSION,
      },
    );
  }

  async applyVariable(setVariable: SetVariable) {
    if (this.request.response != null) {
      await this.applyVariableFromResponse(setVariable, this.request.response, this.request);
    }
  }

  applyJsonPathToResponse(
    setVariable: SetVariable,
    response: HttpResponse | undefined,
  ): string | undefined {
    const jsonPath = setVariable.jsonPath?.trim();
    if (!jsonPath) {
      return undefined;
    } else {
      const responseJson = response?.payload ?? '';
      try {
        const response = JSON.parse(responseJson);
        const result = JSONPath({
          path: jsonPath,
          json: response,
          eval: false,
        });
        let firstResult = result?.[0];
        if (typeof firstResult === 'object') {
          firstResult = JSON.stringify(firstResult);
        }
        return firstResult;
      } catch (e: any) {
        console.error('Error applying JSONPath to response');
        return undefined;
      }
    }
  }

  private async setResponseError(originatingRequestId: string, error?: any) {
    if (this.request.id != originatingRequestId) {
      return console.debug(
        'Request id has changed before the response was returned and will not be updated with the error response',
        originatingRequestId,
      );
    }
    await this.messageService.error({
      title: 'Error performing the request',
      message: 'HTTP call failed',
      detail: error?.message,
    });
    this.request.response = undefined;
    this.requestInProgress = false;
  }

  private formatAsJson(value?: any): any | undefined {
    if (typeof value !== 'string') {
      return value;
    }
    const stringValue = value as string;
    try {
      return JSON.stringify(JSON.parse(stringValue), null, 2);
    } catch {
      return value;
    }
  }

  saveRequest(): Promise<void> {
    return this.projects.saveProjects().then(() => {
      this.#requestUpdatedSubject.next(this.request);
    });
  }
}

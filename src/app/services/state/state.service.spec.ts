import { StateService } from './state.service';
import { TestBed } from '@angular/core/testing';
import { SystemService } from '../system/system.service';
import { ProjectState } from '../../model/state';

describe('StateService', () => {
  let systemService: SystemService;
  let service: StateService;

  async function setUp(state: ProjectState | undefined) {
    systemService = jasmine.createSpyObj('SystemService', {
      ['loadValue']: Promise.resolve(state ? JSON.stringify(state) : undefined),
      ['saveValue']: Promise.resolve(),
    });
    TestBed.configureTestingModule({
      providers: [
        StateService,
        {
          provide: SystemService,
          useValue: systemService,
        },
      ],
    });
    service = TestBed.inject(StateService);
    await service.loadProjectsState();
  }

  it('should clean up state of old ids', async () => {
    await setUp({
      itemState: {
        i1: { expanded: true },
        i2: { expanded: false },
        i3: { expanded: true },
      },
    });
    service.cleanUpProjectState(['i2', 'i3']);
    const actual = service.state.itemState;
    expect(actual).toEqual({
      i2: { expanded: false },
      i3: { expanded: true },
    });
  });

  it('should not clean up anything if all ids are the same', async () => {
    await setUp({
      itemState: {
        i1: { expanded: true },
        i2: { expanded: false },
        i3: { expanded: true },
      },
    });
    service.cleanUpProjectState(['i1', 'i2', 'i3']);
    const actual = service.state.itemState;
    expect(actual).toEqual({
      i1: { expanded: true },
      i2: { expanded: false },
      i3: { expanded: true },
    });
  });

  it('should clean everything up anything if all ids are different', async () => {
    await setUp({
      itemState: {
        i1: { expanded: true },
        i2: { expanded: false },
        i3: { expanded: true },
      },
    });
    service.cleanUpProjectState(['c1', 'c2', 'c3']);
    const actual = service.state.itemState;
    expect(actual).toEqual({});
  });

  it('should handle empty state', async () => {
    await setUp(undefined);
    service.cleanUpProjectState(['c1', 'c2', 'c3']);
    const actual = service.state.itemState;
    expect(actual).toEqual({});
  });
});

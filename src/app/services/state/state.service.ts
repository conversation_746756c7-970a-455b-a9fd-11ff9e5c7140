import { Injectable } from '@angular/core';
import { ProjectItemId, ProjectItemState, ProjectState } from '../../model/state';
import { SystemService } from '../system/system.service';
import { SCRATCHES_PROJECT_ID } from '../projects/projects.service';

const PROJECTS_STATE = 'projects-state';

@Injectable()
export class StateService {
  #state: ProjectState;

  constructor(private readonly system: SystemService) {
    this.#state = {
      itemState: {},
    };
  }

  async initialise() {
    await this.loadProjectsState();
  }

  get state(): Readonly<ProjectState> {
    return { ...this.#state };
  }

  async loadProjectsState(): Promise<void> {
    const defaultProjectsState: ProjectState = {
      itemState: {
        [SCRATCHES_PROJECT_ID]: { expanded: true },
      },
    };

    function parseProjectsStateJson(projectsStateJson: string | undefined): ProjectState {
      if (projectsStateJson == null) {
        return defaultProjectsState;
      } else {
        try {
          return JSON.parse(projectsStateJson);
        } catch (e) {
          console.error(`Error parsing projects state value after loading it from system`, e);
          return defaultProjectsState;
        }
      }
    }

    const projectsStateJson = await this.system.loadValue(PROJECTS_STATE);
    this.#state = parseProjectsStateJson(projectsStateJson);
  }

  cleanUpProjectState(existingItemIds: string[]) {
    const newState: Record<ProjectItemId, ProjectItemState> = {};
    Object.entries(this.#state.itemState)
      .filter((e) => existingItemIds.includes(e[0]))
      .map((e) => (newState[e[0]] = e[1]));
    this.#state.itemState = newState;
  }

  async saveProjectTreeState() {
    try {
      const projectsStateJson = JSON.stringify(this.#state);
      await this.system.saveValue(PROJECTS_STATE, projectsStateJson);
    } catch (e) {
      console.error(`Error saving projects state`, e);
    }
  }

  updateSelection(selectedItemId: ProjectItemId) {
    if (this.#state.selectedItemId !== selectedItemId) {
      this.#state.selectedItemId = selectedItemId;
      void this.saveProjectTreeState();
    }
  }

  clearSelection() {
    if (this.#state.selectedItemId != null) {
      this.#state.selectedItemId = undefined;
      void this.saveProjectTreeState();
    }
  }

  updateItemState(itemId: string, itemState: { expanded: boolean }) {
    if (this.#state.itemState[itemId]?.expanded !== itemState.expanded) {
      if (!itemState.expanded) {
        delete this.#state.itemState[itemId];
      } else {
        this.#state.itemState[itemId] = { expanded: itemState.expanded };
      }
      void this.saveProjectTreeState();
    }
  }
}

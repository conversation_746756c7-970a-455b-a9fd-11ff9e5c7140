import { Injectable } from '@angular/core';
import { SystemService } from './system.service';
import { UserMessage } from '../../../common/system';

@Injectable()
export class UserMessageService {
  constructor(private readonly systemService: SystemService) {}

  info(message: Pick<UserMessage, 'message' | 'title' | 'detail'>): Promise<void> {
    return this.showUserMessage({
      ...message,
      confirm: undefined,
      cancel: 'Close',
      messageType: 'info',
    });
  }

  error(message: Pick<UserMessage, 'message' | 'title' | 'detail'>): Promise<void> {
    return this.showUserMessage({
      ...message,
      confirm: undefined,
      cancel: 'Close',
      messageType: 'error',
    });
  }

  confirm(
    message: Pick<UserMessage, 'message' | 'title' | 'detail'>,
    confirm?: string,
    cancel?: string,
  ): Promise<void> {
    return this.showUserMessage({
      ...message,
      confirm: confirm ?? 'OK',
      cancel: cancel ?? 'Cancel',
      messageType: 'question',
    });
  }

  private showUserMessage(message: UserMessage): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      this.systemService.messageUser(message).then((result) => {
        if (result) {
          resolve();
        } else {
          reject();
        }
      });
    });
  }
}

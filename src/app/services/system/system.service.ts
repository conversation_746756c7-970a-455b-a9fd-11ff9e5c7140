import { Injectable } from '@angular/core';
import { UserMessage, ISystemService, SystemServiceName } from '../../../common/system';
import { BackendService } from '../backend/backend.service';

@Injectable()
export class SystemService implements ISystemService {
  constructor(private backedService: BackendService) {}

  ping(): Promise<any> {
    return this.backedService.callAsync(`${SystemServiceName}.${this.ping.name}`, undefined);
  }

  messageUser(message: UserMessage): Promise<boolean> {
    return this.backedService.callAsync(`${SystemServiceName}.${this.messageUser.name}`, {
      message,
    });
  }

  loadValue(name: string): Promise<string | undefined> {
    return this.backedService.callAsync(`${SystemServiceName}.${this.loadValue.name}`, { name });
  }

  saveValue(name: string, value: string): Promise<void> {
    return this.backedService.callAsync(`${SystemServiceName}.${this.saveValue.name}`, {
      name,
      value,
    });
  }
}

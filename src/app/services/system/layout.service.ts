import { Injectable } from '@angular/core';
import { SystemService } from './system.service';

const LAYOUT_STATE = 'layout-state';

@Injectable()
export class LayoutService {
  #layoutState: LayoutState = { ratios: {} };

  constructor(private readonly systemService: SystemService) {}

  async initialise() {
    try {
      const layoutStateJson = await this.systemService.loadValue(LAYOUT_STATE);
      if (layoutStateJson != null) {
        this.#layoutState = JSON.parse(layoutStateJson);
      }
    } catch (e) {
      console.error('Error loading layoutState', e);
    }
  }

  async persistRatio(id: string, ratio: number) {
    this.#layoutState.ratios[id] = ratio;
    await this.saveLayoutState();
  }

  async getRatio(id: string): Promise<number | undefined> {
    return this.#layoutState.ratios?.[id];
  }

  private async saveLayoutState() {
    if (this.#layoutState != null) {
      try {
        const layoutStateJson = JSON.stringify(this.#layoutState);
        await this.systemService.saveValue(LAYOUT_STATE, layoutStateJson);
      } catch (e) {
        console.error('Error saving layoutState', e);
      }
    }
  }
}

export type LayoutState = {
  ratios: Record<string, number>;
};

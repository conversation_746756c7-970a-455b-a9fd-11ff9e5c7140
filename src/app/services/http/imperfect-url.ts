import { RequestQueryParam } from '../../model/request';

export class ImperfectUrl {
  constructor(private readonly urlString: string) {}

  extractQueryParamsFromUrl(): RequestQueryParam[] {
    try {
      const params: RequestQueryParam[] = [];
      const url = new URL(this.urlString);
      url.searchParams.forEach((value, name) =>
        params.push({
          name,
          value,
        }),
      );
      return params;
    } catch (e) {
      if (!this.urlString.includes('?')) {
        return [];
      }
      return this.urlString
        .slice(this.urlString.indexOf('?') + 1)
        .split('&')
        .map((kv) => kv.split('='))
        .map((kv) => ({
          name: kv[0] ?? '',
          value: kv[1],
        }));
    }
  }

  withoutQueryParams(): string {
    try {
      const url = new URL(this.urlString);
      const port = url.port ? `:${url.port}` : '';
      return `${url.protocol}//${url.hostname}${port}${url.pathname}${url.hash}`;
    } catch (e) {
      if (!this.urlString.includes('?')) {
        return this.urlString;
      }
      return this.urlString.slice(0, this.urlString.indexOf('?'));
    }
  }
}

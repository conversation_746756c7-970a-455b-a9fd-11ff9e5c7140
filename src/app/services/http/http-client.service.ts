import { Injectable } from '@angular/core';
import {
  HttpClientServiceName,
  HttpRequest,
  HttpResponse,
  IHttpClientService,
} from '../../../common/http-client';
import { BackendService } from '../backend/backend.service';

@Injectable()
export class HttpClientService implements IHttpClientService {
  constructor(private backendService: BackendService) {}

  exchange(request: HttpRequest): Promise<HttpResponse> {
    return this.backendService.callAsync(`${HttpClientServiceName}.${this.exchange.name}`, {
      request,
    });
  }
}

import { Request } from './request';

export type Project = {
  id: ProjectId;
  name: string;
  items: ProjectItem[];
};

export type ProjectFolder = {
  id: string;
  name: string;
  items: ProjectItem[];
};

export type ProjectItem = {
  type: ProjectItemType;
  value: Request | ProjectFolder;
};
export type ProjectItemRequest = ProjectItem & { value: Request };
export type ProjectItemFolder = ProjectItem & { value: ProjectFolder };

export enum ProjectItemType {
  REQUEST,
  FOLDER,
}

export type ProjectId = string;

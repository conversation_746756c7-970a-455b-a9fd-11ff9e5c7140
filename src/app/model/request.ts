import { HttpResponse } from '../../common/http-client';
import { v4 as uuidv4 } from 'uuid';

export type Request = {
  id: RequestId;
  name: string;
  method: RequestMethod;
  url: string;
  queryParams: RequestQueryParam[];
  headers: RequestHeader[];
  payload: RequestPayload;
  setVariables?: SetVariable[];
  response?: HttpResponse;
};

export type RequestId = string;

export enum RequestMethod {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
  OPTIONS = 'OPTIONS',
  HEAD = 'HEAD',
  TRACE = 'TRACE',
}

export const SUPPORTED_REQUEST_METHODS = [
  RequestMethod.GET,
  RequestMethod.POST,
  RequestMethod.PUT,
  RequestMethod.PATCH,
  RequestMethod.DELETE,
  RequestMethod.OPTIONS,
  RequestMethod.HEAD,
  RequestMethod.TRACE,
];

export type RequestQueryParam = {
  name: string;
  value?: string;
  disabled?: boolean;
};

export type RequestHeader = {
  name: string;
  value?: string;
  disabled?: boolean;
};

export const DEFAULT_HEADERS: RequestHeader[] = [
  {
    name: 'User-Agent',
    value: 'Headrest/1',
  },
  {
    name: 'Accept',
    value: '*/*',
  },
  {
    name: 'Accept-Encoding',
    value: 'gzip, deflate, br',
  },
  {
    name: 'Connection',
    value: 'keep-alive',
  },
];

export type RequestPayload = {
  type: PayloadType;
  rawPayload: RawPayload;
  formPayload: FormPayload;
};

export enum PayloadType {
  NONE,
  RAW,
  FORM,
}

export type RawPayload = {
  type: RawPayloadType;
  body: string;
};

export enum RawPayloadType {
  JSON = 'json',
  XML = 'xml',
  OTHER = 'other',
}

export type FormPayload = {
  params: RequestFormParam[];
};

export type RequestFormParam = {
  name: string;
  value?: string;
  disabled?: boolean;
};

export function createRequestId() {
  return `request-${uuidv4()}`;
}

export function createDefaultRequest(): Request {
  return {
    id: createRequestId(),
    name: 'New request',
    method: RequestMethod.GET,
    url: '',
    queryParams: [],
    headers: DEFAULT_HEADERS,
    payload: {
      type: PayloadType.NONE,
      rawPayload: { type: RawPayloadType.JSON, body: '' },
      formPayload: { params: [] },
    },
    setVariables: [],
  };
}

export function createProjectId() {
  return `project-${uuidv4()}`;
}

export function createProjectFolderId() {
  return `folder-${uuidv4()}`;
}

export type SetVariable = {
  variableKey: string;
  jsonPath: string;
};

export type LocalRequestSessions = {
  sessions: Record<RequestId, RequestSession[]>;
};

export type RequestSession = Request & {
  id: string;
  requestId: string;
  response?: HttpResponse;
  created: Date;
};

// XXX saving the request sessions in a file per the requestId makes it easier to load.
// However, if the user makes a lot of requests, this could result in a large file.
// Question: is it better to save the responses separately?

import { EnvironmentId } from './environment';
import { ProjectId } from './project';

export type Variable = {
  key: VariableKey;
  staticValues?: Record<EnvironmentId, VariableValue>;
};

export type VariableKey = string;
export type VariableValue = string;

export type Variables = {
  globalScope: Variable[];
  projectScope: Record<ProjectId, Variable[]>;
};

export type VariableValues = {
  globalScope: Record<VariableKey, Record<EnvironmentId, VariableValueSlot>>;
  projectScope: Record<ProjectId, Record<VariableKey, Record<EnvironmentId, VariableValueSlot>>>;
};

export type VariableValueSlot = {
  value: VariableValue | undefined;
  timestamp: Date;
  sourceId: string;
  sourceType: VariableValueSourceType;
};

export enum VariableValueSourceType {
  REQUEST_SESSION = 'REQUEST_SESSION',
}

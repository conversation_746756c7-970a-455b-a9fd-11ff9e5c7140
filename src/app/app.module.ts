import { NgMod<PERSON>, Provider, inject, provideAppInitializer } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { MAT_RIPPLE_GLOBAL_OPTIONS } from '@angular/material/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { environment } from '../environments/environment';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { MacOSBackendService } from './services/backend/macos-backend.service';
import { SystemService } from './services/system/system.service';
import { MainComponent } from './ui/main.component';
import { MatButtonModule } from '@angular/material/button';
import { WorkspaceComponent } from './ui/workspace/workspace.component';
import { RequestViewComponent } from './ui/request/request-view.component';
import { HttpClientService } from './services/http/http-client.service';
import { FormsModule } from '@angular/forms';
import { MatSelectModule } from '@angular/material/select';
import { RequestBarComponent } from './ui/request/request-bar/request-bar.component';
import { RequestDataComponent } from './ui/request/request-data/request-data.component';
import { RequestHeadersComponent } from './ui/request/request-data/request-headers/request-headers.component';
import { MatMenuModule } from '@angular/material/menu';
import { MatTabsModule } from '@angular/material/tabs';
import { RequestQueryParamsComponent } from './ui/request/request-data/request-query-params/request-query-params.component';
import { SplitViewModule } from './ui/_common/split-view/split-view.module';
import { RequestBodyComponent } from './ui/request/request-data/request-body/request-body.component';
import { RequestResponseComponent } from './ui/request/request-data/request-response/request-response.component';
import { BackendService } from './services/backend/backend.service';
import {
  WebBackendService,
  WebHttpClientService,
  WebSystemService,
} from './services/backend/web-backend.service';
import { ElectronBackendService } from './services/backend/electron-backend.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { NameValueComponent } from './ui/_common/name-value/name-value.component';
import { CodeEditorComponent } from './ui/_common/code-editor/code-editor.component';
import { RequestBodyNoneComponent } from './ui/request/request-data/request-body/request-body-none/request-body-none.component';
import { RequestBodyRawComponent } from './ui/request/request-data/request-body/request-body-raw/request-body-raw.component';
import { RequestBodyFormComponent } from './ui/request/request-data/request-body/request-body-form/request-body-form.component';
import { RequestResponseHeadersComponent } from './ui/request/request-data/request-response/request-response-headers/request-response-headers.component';
import { NavigatorComponent } from './ui/navigator/navigator.component';
import { ProjectsComponent } from './ui/navigator/projects/projects.component';
import { RequestSessionService } from './services/request-session/request-session.service';
import { ProjectsService } from './services/projects/projects.service';
import { MessageDialogComponent } from './ui/_common/message-dialog/message-dialog.component';
import { UserMessageService } from './services/system/user-message.service';
import { MatDialogModule } from '@angular/material/dialog';
import { TreeModule } from './ui/_common/tree/tree.module';
import { ProjectsTreeService } from './ui/navigator/projects/projects-tree.service';
import { StateService } from './services/state/state.service';
import { TruncatePipe } from './ui/_common/pipes/truncate.pipe';
import { VariablesViewDialogComponent } from './ui/variables/variables-view-dialog.component';
import { RequestResponseSummaryComponent } from './ui/request/request-data/request-response/request-response-summary/request-response-summary.component';
import { RequestResponseVariablesComponent } from './ui/request/request-data/request-response/request-response-variables/request-response-variables.component';
import { LayoutService } from './services/system/layout.service';
import { RequestResponseVariablesListingComponent } from './ui/request/request-data/request-response/request-response-variables/request-response-variables-listing/request-response-variables-listing.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { RequestResponseVariableEditComponent } from './ui/request/request-data/request-response/request-response-variables/request-response-variables-listing/request-response-variable-edit/request-response-variable-edit.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { EnvironmentSelectorComponent } from './ui/environments/environment-selector/environment-selector.component';
import { VariablesService } from './services/variables/variables.service';
import { EnvironmentsService } from './services/variables/environments.service';
import { EnvironmentsDialogComponent } from './ui/environments/environments-dialog/environments-dialog.component';
import { EnvironmentsViewComponent } from './ui/environments/environments-view/environments-view.component';
import { EnvironmentViewEditComponent } from './ui/environments/environments-view/environment-view-edit/environment-view-edit.component';
import { VariableComponent } from './ui/variables/variable/variable.component';
import { VariableNewDialogComponent } from './ui/variables/variable-new/variable-new-dialog.component';

console.log('Browser', navigator.userAgent);

let backendServiceProvider: Provider;
let extraBackendServices: Provider[] = [];
const macOS = MacOSBackendService.isSupported();
const electron = ElectronBackendService.isSupported();
if (macOS) {
  console.info('Backend Service Provider', 'MacOSBackendService');
  backendServiceProvider = {
    provide: BackendService,
    useClass: MacOSBackendService,
  };
} else if (electron) {
  console.info('Backend Service Provider', 'ElectronBackendService');
  backendServiceProvider = {
    provide: BackendService,
    useClass: ElectronBackendService,
  };
} else {
  console.info('Backend Service Provider', 'WebBackendService');
  backendServiceProvider = {
    provide: BackendService,
    useClass: WebBackendService,
  };
  extraBackendServices = [WebSystemService, WebHttpClientService];
}

@NgModule({
  declarations: [
    AppComponent,
    TruncatePipe,
    MainComponent,
    WorkspaceComponent,
    RequestViewComponent,
    RequestBarComponent,
    RequestDataComponent,
    RequestHeadersComponent,
    RequestQueryParamsComponent,
    RequestBodyComponent,
    RequestResponseComponent,
    NameValueComponent,
    CodeEditorComponent,
    RequestBodyNoneComponent,
    RequestBodyRawComponent,
    RequestBodyFormComponent,
    RequestResponseHeadersComponent,
    NavigatorComponent,
    ProjectsComponent,
    MessageDialogComponent,
    VariablesViewDialogComponent,
    RequestResponseSummaryComponent,
    RequestResponseVariablesComponent,
    RequestResponseVariablesListingComponent,
    RequestResponseVariableEditComponent,
    EnvironmentSelectorComponent,
    EnvironmentsDialogComponent,
    EnvironmentsViewComponent,
    EnvironmentViewEditComponent,
    VariableComponent,
    VariableNewDialogComponent,
  ],
  bootstrap: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    FormsModule,
    // FontAwesome
    FontAwesomeModule,
    // Own
    SplitViewModule,
    TreeModule,
    // Material
    NoopAnimationsModule,
    MatButtonModule,
    MatSelectModule,
    MatMenuModule,
    MatTabsModule,
    MatDialogModule,
    MatExpansionModule,
    MatAutocompleteModule,
  ],
  providers: [
    // Material
    {
      // no ripple per se, just a quick flash
      provide: MAT_RIPPLE_GLOBAL_OPTIONS,
      useValue: {
        disabled: false,
        animation: {
          enterDuration: 0,
          exitDuration: 50,
        },
      },
    },
    // IBackendService
    backendServiceProvider,
    ...extraBackendServices,
    // UI Services
    ProjectsTreeService,
    UserMessageService,
    // Services
    SystemService,
    HttpClientService,
    RequestSessionService,
    ProjectsService,
    StateService,
    LayoutService,
    VariablesService,
    EnvironmentsService,
    // Initializer
    provideAppInitializer(() => {
      const initializerFn = initialiseServices(
        inject(EnvironmentsService),
        inject(ProjectsService),
        inject(StateService),
        inject(VariablesService),
        inject(LayoutService),
      );
      return initializerFn();
    }),
    provideHttpClient(withInterceptorsFromDi()),
  ],
})
export class AppModule {
  constructor() {
    console.info('Prod environment', environment.production);
    if (environment.production) {
      console.debug = (...data: any[]) => {
        if ((<any>window).KINGSTON_DEV) {
          return console.debug(['DBG', ...data]);
        }
      };
    }
  }
}

function initialiseServices(
  environmentsService: EnvironmentsService,
  projectsService: ProjectsService,
  stateService: StateService,
  variablesService: VariablesService,
  layoutService: LayoutService,
) {
  return async () => {
    await environmentsService.initialise();
    await projectsService.initialise();
    await stateService.initialise();
    await variablesService.initialise();
    await layoutService.initialise();
  };
}

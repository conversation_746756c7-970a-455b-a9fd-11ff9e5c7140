import { Component } from '@angular/core';
import { RequestSessionService } from '../../../../services/request-session/request-session.service';
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { RequestHeader } from '../../../../model/request';

@Component({
  selector: 'kng-request-headers',
  templateUrl: './request-headers.component.html',
  styleUrls: ['./request-headers.component.scss'],
  standalone: false,
})
export class RequestHeadersComponent {
  private currentHeaderIndex?: number;
  focusOnceIndex?: number;

  readonly icons = {
    headerAdd: faPlus,
  };

  constructor(public requestSessionService: RequestSessionService) {}

  selectHeader(index: number) {
    this.currentHeaderIndex = index;
  }

  get currentHeader(): RequestHeader | undefined {
    if (this.currentHeaderIndex == null) {
      return undefined;
    }
    return this.requestSessionService.request.headers[this.currentHeaderIndex];
  }

  deleteCurrentHeader() {
    if (this.currentHeaderIndex != null) {
      this.requestSessionService.deleteHeaderAt(this.currentHeaderIndex);
      this.currentHeaderIndex = undefined;
    }
  }

  disableCurrentHeader() {
    if (this.currentHeaderIndex != null && this.currentHeader) {
      this.requestSessionService.disableHeaderAt(this.currentHeaderIndex);
      this.currentHeaderIndex = undefined;
    }
  }

  enableCurrentHeader() {
    if (this.currentHeaderIndex != null && this.currentHeader) {
      this.requestSessionService.enableHeaderAt(this.currentHeaderIndex);
      this.currentHeaderIndex = undefined;
    }
  }

  addNewHeader() {
    this.requestSessionService.addHeader();
    this.focusOnceIndex = this.requestSessionService.request.headers.length - 1;
  }

  nameOrValueChanged() {
    void this.requestSessionService.saveRequest();
  }
}

<div class="request-headers">
  @for (header of requestSessionService.request.headers; track header; let i = $index) {
    <kng-name-value
      [item]="header"
      [currentItem]="currentHeader"
      [focusOnce]="i === focusOnceIndex"
      nameLabel="Header name"
      valueLabel="Header value"
      (select)="selectHeader(i)"
      (disable)="disableCurrentHeader()"
      (enable)="enableCurrentHeader()"
      (delete)="deleteCurrentHeader()"
      (nameChanged)="nameOrValueChanged()"
      (valueChanged)="nameOrValueChanged()"
    ></kng-name-value>
  }
  <div class="request-headers--add">
    <button mat-button color="primary" title="Add a new header" (click)="addNewHeader()">
      <fa-icon [icon]="icons.headerAdd"></fa-icon>
    </button>
  </div>
</div>

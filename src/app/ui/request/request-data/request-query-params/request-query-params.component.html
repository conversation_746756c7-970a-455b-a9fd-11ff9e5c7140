<div class="request-query-params">
  @for (queryParam of requestSessionService.request.queryParams; track queryParam; let i = $index) {
    <kng-name-value
      [item]="queryParam"
      [focusOnce]="i === focusOnceIndex"
      [currentItem]="currentQueryParam"
      nameLabel="Query param name"
      valueLabel="Query param value"
      (nameChanged)="paramChanged()"
      (valueChanged)="paramChanged()"
      (select)="selectQueryParam(i)"
      (disable)="disableQueryParam()"
      (enable)="enableCurrentQueryParam()"
      (delete)="deleteCurrentQueryParam()"
    ></kng-name-value>
  }
  <div class="request-query-params--add">
    <button mat-button color="primary" title="Add a new query param" (click)="addNewQueryParam()">
      <fa-icon [icon]="icons.add"></fa-icon>
    </button>
  </div>
</div>

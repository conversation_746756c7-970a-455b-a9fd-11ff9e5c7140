import { Component } from '@angular/core';
import { RequestSessionService } from '../../../../services/request-session/request-session.service';
import { faEllipsisVertical, faPlus } from '@fortawesome/free-solid-svg-icons';
import { faEye, faEyeSlash, faTrashCan } from '@fortawesome/free-regular-svg-icons';
import { RequestQueryParam } from '../../../../model/request';
import { ImperfectUrl } from '../../../../services/http/imperfect-url';

@Component({
  selector: 'kng-request-query-params',
  templateUrl: './request-query-params.component.html',
  styleUrls: ['./request-query-params.component.scss'],
  standalone: false,
})
export class RequestQueryParamsComponent {
  #currentQueryParamIndex?: number;
  focusOnceIndex?: number;

  readonly icons = {
    menu: faEllipsisVertical,
    add: faPlus,
    delete: faTrashCan,
    disable: faEyeSlash,
    enable: faEye,
  };

  constructor(public requestSessionService: RequestSessionService) {}

  private updateUrl() {
    const queryString = this.asQueryString(this.requestSessionService.request.queryParams);
    const urlWithoutQueryString = new ImperfectUrl(
      this.requestSessionService.url,
    ).withoutQueryParams();
    const newUrl = urlWithoutQueryString + (queryString.length > 0 ? '?' : '') + queryString;
    if (this.requestSessionService.url !== newUrl) {
      this.requestSessionService.url = newUrl;
    }
  }

  private asQueryString(queryParams: RequestQueryParam[]): string {
    return queryParams
      .filter((param) => this.isActionable(param))
      .map((param) => `${param.name}=${param.value ?? ''}`)
      .join('&');
  }

  private isActionable(queryParam: RequestQueryParam): boolean {
    return !queryParam.disabled && (queryParam.name.trim() !== '' || queryParam.value != null);
  }

  paramChanged() {
    this.updateUrl();
  }

  selectQueryParam(index: number) {
    this.#currentQueryParamIndex = index;
  }

  get currentQueryParam(): RequestQueryParam | undefined {
    if (this.#currentQueryParamIndex == null) {
      return undefined;
    }
    return this.requestSessionService.request.queryParams[this.#currentQueryParamIndex];
  }

  deleteCurrentQueryParam() {
    if (this.#currentQueryParamIndex != null) {
      this.requestSessionService.request.queryParams.splice(this.#currentQueryParamIndex, 1);
      this.#currentQueryParamIndex = undefined;
      this.updateUrl();
    }
  }

  disableQueryParam() {
    if (this.#currentQueryParamIndex != null && this.currentQueryParam) {
      this.requestSessionService.request.queryParams[this.#currentQueryParamIndex!].disabled = true;
      this.#currentQueryParamIndex = undefined;
      this.updateUrl();
    }
  }

  enableCurrentQueryParam() {
    if (this.#currentQueryParamIndex != null && this.currentQueryParam) {
      this.requestSessionService.request.queryParams[this.#currentQueryParamIndex!].disabled =
        false;
      this.#currentQueryParamIndex = undefined;
      this.updateUrl();
    }
  }

  addNewQueryParam() {
    this.requestSessionService.request.queryParams.push({ name: '' });
    void this.requestSessionService.saveRequest();
    this.focusOnceIndex = this.requestSessionService.request.queryParams.length - 1;
  }
}

import { Component } from '@angular/core';
import { RequestSessionService } from '../../../../services/request-session/request-session.service';
import { HttpResponse } from '../../../../../common/http-client';
import { faChevronUp } from '@fortawesome/pro-light-svg-icons';

@Component({
  selector: 'kng-request-response',
  templateUrl: './request-response.component.html',
  styleUrls: ['./request-response.component.scss'],
  standalone: false,
})
export class RequestResponseComponent {
  responseView: 'body' | 'headers' = 'body';
  variablesExpanded = false;

  icons = {
    expand: faChevronUp,
  };

  constructor(private readonly requestSessionService: RequestSessionService) {}

  get response(): HttpResponse | undefined {
    if (!this.requestSessionService.requestInProgress) {
      return this.requestSessionService.request.response;
    }
    return;
  }

  get setVariablesCount(): number {
    return this.requestSessionService.request.setVariables?.length ?? 0;
  }

  get requestInProgress(): boolean {
    return this.requestSessionService.requestInProgress;
  }

  collapseVariables() {
    this.variablesExpanded = false;
  }
}

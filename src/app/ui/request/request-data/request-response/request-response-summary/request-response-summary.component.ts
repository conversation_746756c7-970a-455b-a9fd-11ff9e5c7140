import { Component, EventEmitter, Input, Output } from '@angular/core';
import { RequestSessionService } from '../../../../../services/request-session/request-session.service';
import { HttpResponse } from '../../../../../../common/http-client';
import { faCircle } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'kng-request-response-summary',
  templateUrl: './request-response-summary.component.html',
  styleUrls: ['./request-response-summary.component.scss'],
  standalone: false,
})
export class RequestResponseSummaryComponent {
  @Input() responseView: 'body' | 'headers' = 'body';
  @Output() responseViewChange = new EventEmitter<'body' | 'headers'>();

  icons = {
    httpStatus: faCircle,
  };

  constructor(private readonly requestSessionService: RequestSessionService) {}

  get response(): HttpResponse | undefined {
    if (!this.requestSessionService.requestInProgress) {
      return this.requestSessionService.request.response;
    }
    return;
  }

  get responseDuration(): number | undefined {
    if (this.response?.info?.completedAt != null && this.response.info.initiatedAt != null) {
      return this.response.info.completedAt - this.response.info.initiatedAt;
    }
    return undefined;
  }

  get responseDate(): string | undefined {
    const completedAtMS = this.response?.info?.completedAt;
    if (completedAtMS != null) {
      const completedDate = new Date(completedAtMS);
      return Intl.DateTimeFormat(undefined, {
        dateStyle: 'medium',
        timeStyle: 'long',
      }).format(completedDate);
    }
    return undefined;
  }

  get requestInProgress(): boolean {
    return this.requestSessionService.requestInProgress;
  }
}

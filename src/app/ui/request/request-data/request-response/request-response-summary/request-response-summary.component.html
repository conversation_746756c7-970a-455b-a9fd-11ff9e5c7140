<div class="response-summary">
  @if (response != null) {
    <div
      class="response-summary--status"
      [ngClass]="{
        positive: response.status >= 200 && response.status < 300,
        negative: !(response.status >= 200 && response.status < 300),
      }"
    >
      <fa-icon [icon]="icons.httpStatus"></fa-icon>
      HTTP {{ response.status }}
    </div>
  }
  @if (response != null) {
    <div class="response-summary--view-type">
      @if (responseView !== "headers") {
        <button class="link" (click)="responseViewChange.emit('headers')">Response headers</button>
      }
      @if (responseView !== "body") {
        <button class="link" (click)="responseViewChange.emit('body')">Response body</button>
      }
    </div>
  }
  @if (responseDuration) {
    <div class="response-summary--duration" [title]="responseDate">
      {{ responseDuration | number }}
      ms
    </div>
  }
  @if (response == null) {
    <div class="response-summary--waiting">
      @if (!requestInProgress) {
        <span>Request not sent yet</span>
      }
      @if (requestInProgress) {
        <span>Request in progress...</span>
      }
    </div>
  }
</div>

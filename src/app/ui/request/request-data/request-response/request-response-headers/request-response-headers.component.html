<div class="request-response-headers">
  @if (
    !requestSessionService.request.response?.headers ||
    requestSessionService.request.response?.headers?.length === 0
  ) {
    <div class="request-response-headers--none">
      <div><fa-icon [icon]="icons.header"></fa-icon></div>
      This response did not return any headers.
    </div>
  }
  @if ((requestSessionService.request.response?.headers?.length ?? 0) > 0) {
    <table class="request-response-headers--table">
      <thead>
        <tr>
          <td>Header name</td>
          <td>Header value</td>
        </tr>
      </thead>
      <tbody>
        @for (header of requestSessionService.request.response?.headers ?? []; track header) {
          @for (headerValue of header.values; track headerValue) {
            <tr>
              <td>{{ header.name }}</td>
              <td>{{ headerValue ?? "" }}</td>
            </tr>
          }
        }
      </tbody>
    </table>
  }
</div>

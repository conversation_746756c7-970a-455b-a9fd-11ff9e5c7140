import { Component } from '@angular/core';
import { RequestSessionService } from '../../../../../services/request-session/request-session.service';
import { faH } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'kng-request-response-headers',
  templateUrl: './request-response-headers.component.html',
  styleUrls: ['./request-response-headers.component.scss'],
  standalone: false,
})
export class RequestResponseHeadersComponent {
  icons = {
    header: faH,
  };

  constructor(public requestSessionService: RequestSessionService) {}
}

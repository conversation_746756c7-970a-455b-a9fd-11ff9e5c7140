import { Component, EventEmitter, Output } from '@angular/core';
import { faChevronDown } from '@fortawesome/pro-light-svg-icons';
import { RequestSessionService } from '../../../../../services/request-session/request-session.service';
import { SetVariable } from '../../../../../model/request';
import { VariablesService } from '../../../../../services/variables/variables.service';
import { Variable } from '../../../../../model/variable';
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { faRabbitRunning } from '@fortawesome/pro-solid-svg-icons';

@Component({
  selector: 'kng-request-response-variables',
  templateUrl: './request-response-variables.component.html',
  styleUrls: ['./request-response-variables.component.scss'],
  standalone: false,
})
export class RequestResponseVariablesComponent {
  @Output() collapse = new EventEmitter<never>();

  icons = {
    collapse: faChevronDown,
  };
}

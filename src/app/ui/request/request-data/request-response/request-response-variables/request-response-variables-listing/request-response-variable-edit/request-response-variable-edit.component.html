<div class="variable-edit">
  @if (!varsAvailable) {
    <div class="variable-edit--no-vars">
      <div>There are no variables that can be set</div>
      <p>
        <small>To set a variable, it must first be declared.</small>
      </p>
      <button class="link" title="Declare a new variable" (click)="useVariables()">
        <fa-icon [icon]="icons.var"></fa-icon>
        Declare variables
      </button>
    </div>
  }
  @if (varsAvailable) {
    <div class="variable-edit--values">
      @if (!isUpdate) {
        <input
          class="field"
          placeholder="Variable name"
          [(ngModel)]="variableKey"
          [matAutocomplete]="variablesAutocomplete"
          autocomplete="off"
          autocorrect="off"
          autocapitalize="off"
          spellcheck="false"
        />
      }
      <input
        placeholder="JSONPath"
        class="field"
        [(ngModel)]="jsonPath"
        (keydown.enter)="evaluateNewSetVariable()"
        autocomplete="off"
        autocorrect="off"
        autocapitalize="off"
        spellcheck="false"
      />
      <mat-autocomplete #variablesAutocomplete="matAutocomplete">
        @if (filteredProjectVariables.length > 0) {
          <mat-optgroup label="In Project">
            @for (variable of filteredProjectVariables; track variable) {
              <mat-option [value]="variable.key">
                {{ variable.key }}
              </mat-option>
            }
          </mat-optgroup>
        }
        @if (filteredGlobalVariables.length > 0) {
          <mat-optgroup label="Global">
            @for (variable of filteredGlobalVariables; track variable) {
              <mat-option [value]="variable.key">
                {{ variable.key }}
              </mat-option>
            }
          </mat-optgroup>
        }
      </mat-autocomplete>
      <div class="variable-edit--values--evaluate">
        <button
          class="link icon"
          (click)="evaluateNewSetVariable()"
          title="Evaluate the JSONPath and preview the result below"
        >
          <fa-icon [icon]="icons.evaluate"></fa-icon>
          <span>Evaluate</span>
        </button>
      </div>
      <div class="variable-edit--values--value">
        Value: <input placeholder="value" readonly [value]="evaluationResult" />
      </div>
    </div>
  }
  @if (variableAlreadySet) {
    <div class="variable-edit--validation">
      <div class="variable-edit--validation--message">
        <fa-icon [icon]="icons.warning"></fa-icon>
        Variable <code>{{ variableKey }}</code> is already set.
      </div>
    </div>
  }
  @if (variableKeyNotDeclared) {
    <div class="variable-edit--validation">
      <div class="variable-edit--validation--message">
        <fa-icon [icon]="icons.warning"></fa-icon>
        Variable <code>{{ variableKey }}</code> is not declared.
      </div>
      <button class="link" title="Declare a new variable" (click)="useVariables()">
        <fa-icon [icon]="icons.var"></fa-icon>
        Declare variables
      </button>
    </div>
  }
  <div class="variable-edit--buttons">
    @if (isUpdate) {
      <button mat-button color="primary" (click)="updateVariable()" [disabled]="!isValidUpdate()">
        Update
      </button>
    }
    @if (!isUpdate) {
      <button mat-button color="primary" (click)="setVariable()" [disabled]="!isValidSet()">
        Set
      </button>
    }
    @if (isUpdate) {
      <button mat-button color="warn" (click)="unset.emit()">Un-set</button>
    }
    @if (!isUpdate) {
      <button mat-button (click)="cancel.emit()">Cancel</button>
    }
  </div>
</div>

<div class="variables-listing">
  <mat-accordion class="variables-listing--accordion flat" multi>
    @for (variable of setVariables; track variable) {
      <mat-expansion-panel #currentPanel>
        <mat-expansion-panel-header collapsedHeight="5rem">
          <div class="variables-listing--accordion--header">
            <div class="variables-listing--accordion--header--var">{{ variable.variableKey }}</div>
            <div class="variables-listing--accordion--header--value">
              {{ evaluateVariable(variable.variableKey) }}
            </div>
          </div>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <kng-request-response-variable-edit
            [variable]="variable"
            [evaluationResult]="evaluateVariable(variable.variableKey)"
            [visible]="currentPanel.expanded"
            (set)="updateSetVariable(variable.variableKey, $event, currentPanel)"
            (unset)="deleteSetVariable(variable)"
          ></kng-request-response-variable-edit>
        </ng-template>
      </mat-expansion-panel>
    }
    @if (setNewVariable) {
      <mat-expansion-panel expanded (afterCollapse)="cancelNewSetVariable()">
        <kng-request-response-variable-edit
          (set)="setNewSetVariable($event)"
          (cancel)="cancelNewSetVariable()"
        ></kng-request-response-variable-edit>
      </mat-expansion-panel>
    }
  </mat-accordion>

  @if (!setNewVariable) {
    <div class="variables-listing--new">
      <button mat-button color="primary" title="Set a new variable" (click)="addNewSetVariable()">
        <fa-icon [icon]="icons.add"></fa-icon>
      </button>
    </div>
  }
</div>

import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { SetVariable } from '../../../../../../../model/request';
import { faBracketsCurly, faRabbitRunning } from '@fortawesome/pro-solid-svg-icons';
import { RequestSessionService } from '../../../../../../../services/request-session/request-session.service';
import { Variable } from '../../../../../../../model/variable';
import { VariablesService } from '../../../../../../../services/variables/variables.service';
import { MatDialog } from '@angular/material/dialog';
import { faTriangleExclamation } from '@fortawesome/pro-light-svg-icons';
import { VariablesViewDialogComponent } from '../../../../../../variables/variables-view-dialog.component';
import { changeOf } from '../../../../../../_common/util/changes';

@Component({
  selector: 'kng-request-response-variable-edit',
  templateUrl: './request-response-variable-edit.component.html',
  styleUrls: ['./request-response-variable-edit.component.scss'],
  standalone: false,
})
export class RequestResponseVariableEditComponent implements OnInit, OnChanges {
  variableKey?: string;
  jsonPath?: string;

  @Input() variable?: SetVariable;
  @Input() evaluationResult?: string;
  @Input() visible?: boolean;
  @Output() set = new EventEmitter<SetVariable>();
  @Output() unset = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();

  icons = {
    evaluate: faRabbitRunning,
    var: faBracketsCurly,
    warning: faTriangleExclamation,
  };

  constructor(
    private readonly requestSessionService: RequestSessionService,
    private readonly variablesService: VariablesService,
    private readonly matDialog: MatDialog,
  ) {}

  ngOnInit(): void {
    if (this.variable) {
      this.variableKey = this.variable.variableKey;
      this.jsonPath = this.variable.jsonPath;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    const visibleChange = changeOf(changes, (c: this) => c.visible);
    if (visibleChange.currentValue === false) {
      this.variableKey = this.variable?.variableKey;
      this.jsonPath = this.variable?.jsonPath;
    }
  }

  get isUpdate(): boolean {
    return this.variable != null;
  }

  private get alreadySetVariableKeys() {
    return this.requestSessionService.request.setVariables?.map((v) => v.variableKey) ?? [];
  }

  get availableProjectVariables(): Variable[] {
    const alreadySetVariableKeys = this.alreadySetVariableKeys;
    return this.variablesService
      .variablesInProjectScope(this.requestSessionService.project)
      .filter((v) => !alreadySetVariableKeys.includes(v.key));
  }

  get filteredProjectVariables(): Variable[] {
    const availableProjectVariables = this.availableProjectVariables;
    const typedVariableKey = this.variableKey?.toLowerCase();
    if (typedVariableKey) {
      return availableProjectVariables.filter((v) =>
        v.key.toLowerCase().includes(typedVariableKey),
      );
    }
    return availableProjectVariables;
  }

  get availableGlobalVariables(): Variable[] {
    const alreadySetVariableKeys = this.alreadySetVariableKeys;
    return this.variablesService
      .variablesInGlobalScope()
      .filter((v) => !alreadySetVariableKeys.includes(v.key));
  }

  get filteredGlobalVariables(): Variable[] {
    const availableGlobalVariables = this.availableGlobalVariables;
    const typedVariableKey = this.variableKey?.toLowerCase();
    if (typedVariableKey) {
      return availableGlobalVariables.filter((v) => v.key.toLowerCase().includes(typedVariableKey));
    }
    return availableGlobalVariables;
  }

  get varsAvailable(): boolean {
    return (
      this.isUpdate ||
      this.availableProjectVariables.length > 0 ||
      this.availableGlobalVariables.length > 0
    );
  }

  get variableKeyNotDeclared(): boolean {
    if (!this.variableKey) {
      return false;
    }

    const variableDeclared = [
      ...this.variablesService.variablesInProjectScope(this.requestSessionService.project),
      ...this.variablesService.variablesInGlobalScope(),
    ].some((v) => v.key === this.variableKey);

    return !variableDeclared;
  }

  get variableAlreadySet(): boolean {
    if (!this.variableKey) {
      return false;
    }
    if (this.isUpdate) {
      return false;
    }
    return this.alreadySetVariableKeys.includes(this.variableKey);
  }

  useVariables() {
    this.matDialog.open(VariablesViewDialogComponent);
  }

  evaluateNewSetVariable() {
    if (this.variableKey && this.jsonPath) {
      this.evaluationResult = this.requestSessionService.applyJsonPathToResponse(
        {
          variableKey: this.variableKey,
          jsonPath: this.jsonPath,
        },
        this.requestSessionService.request.response,
      );
    }
  }

  isValidSet(): boolean {
    if (this.variableKey != null && this.jsonPath != null) {
      const validVariableKey = [
        ...this.filteredGlobalVariables,
        ...this.filteredProjectVariables,
      ].some((v) => v.key === this.variableKey);
      const validJsonPath = this.jsonPath.trim() != '';
      return validVariableKey && validJsonPath;
    }
    return false;
  }

  isValidUpdate(): boolean {
    return this.variableKey != null && this.jsonPath != null && this.jsonPath.trim() != '';
  }

  setVariable() {
    if (this.isValidSet()) {
      this.set.emit({
        variableKey: this.variableKey!,
        jsonPath: this.jsonPath!,
      });
    }
  }

  updateVariable() {
    if (this.isUpdate && this.variable != null && this.isValidUpdate()) {
      this.set.emit({
        variableKey: this.variable.variableKey,
        jsonPath: this.jsonPath!,
      });
    }
  }
}

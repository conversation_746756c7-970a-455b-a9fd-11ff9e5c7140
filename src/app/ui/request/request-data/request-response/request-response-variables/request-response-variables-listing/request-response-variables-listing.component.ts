import { Component } from '@angular/core';
import { SetVariable } from '../../../../../../model/request';
import { faChevronDown } from '@fortawesome/pro-light-svg-icons';
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { RequestSessionService } from '../../../../../../services/request-session/request-session.service';
import { VariablesService } from '../../../../../../services/variables/variables.service';
import { MatExpansionPanel } from '@angular/material/expansion';
import { EnvironmentsService } from '../../../../../../services/variables/environments.service';

@Component({
  selector: 'kng-request-response-variables-listing',
  templateUrl: './request-response-variables-listing.component.html',
  styleUrls: ['./request-response-variables-listing.component.scss'],
  standalone: false,
})
export class RequestResponseVariablesListingComponent {
  setNewVariable = false;

  icons = {
    collapse: faChevronDown,
    add: faPlus,
  };

  constructor(
    private readonly requestSessionService: RequestSessionService,
    private readonly variablesService: VariablesService,
    private readonly environmentService: EnvironmentsService,
  ) {}

  get setVariables(): SetVariable[] {
    return [...(this.requestSessionService.request.setVariables ?? [])];
  }

  evaluateVariable(variableKey: string): string | undefined {
    return this.variablesService.evaluateVariable(
      variableKey,
      this.requestSessionService.project?.id,
      this.environmentService.activeEnvironment.id,
    );
  }

  addNewSetVariable() {
    this.setNewVariable = true;
  }

  cancelNewSetVariable() {
    this.setNewVariable = false;
  }

  async setNewSetVariable(newSetVariable: SetVariable) {
    const variableKey = newSetVariable?.variableKey?.trim();
    const jsonPath = newSetVariable?.jsonPath?.trim();
    if (variableKey && jsonPath) {
      await this.requestSessionService.addSetVariable({ variableKey, jsonPath });
      this.cancelNewSetVariable();
    }
  }

  async updateSetVariable(
    variableKey: string,
    updatedVariable: SetVariable,
    currentPanel: MatExpansionPanel,
  ) {
    const jsonPath = updatedVariable.jsonPath.trim();
    if (jsonPath) {
      await this.requestSessionService.updateSetVariable({ variableKey, jsonPath });
      currentPanel.expanded = false;
    }
  }

  async deleteSetVariable(variable: SetVariable) {
    await this.requestSessionService.deleteSetVariable(variable.variableKey);
  }
}

<kng-split-view id="request-response-variables" direction="vertical">
  <kng-split-view-1>
    <div
      class="request-response"
      [ngClass]="{
        'no-footer': variablesExpanded,
      }"
    >
      <kng-request-response-summary [(responseView)]="responseView"></kng-request-response-summary>
      <div class="request-response--view">
        @if (response?.status != null && responseView === "headers") {
          <kng-request-response-headers></kng-request-response-headers>
        }
        @if (response?.payload && responseView === "body") {
          <kng-code-editor [value]="response?.payload ?? ''" [readonly]="true"></kng-code-editor>
        }
      </div>
      @if (!variablesExpanded) {
        <div class="request-response--variables">
          <div class="request-response--variables--expand">
            <button mat-button color="primary" class="small" (click)="variablesExpanded = true">
              Set variables
              <fa-icon [icon]="icons.expand"></fa-icon>
            </button>
          </div>
          <div class="request-response--variables--summary" (dblclick)="variablesExpanded = true">
            Variables from this response
            <span class="badge">{{ setVariablesCount }}</span>
          </div>
        </div>
      }
    </div>
  </kng-split-view-1>
  @if (variablesExpanded) {
    <kng-split-view-2>
      <kng-request-response-variables
        (collapse)="collapseVariables()"
      ></kng-request-response-variables>
    </kng-split-view-2>
  }
</kng-split-view>

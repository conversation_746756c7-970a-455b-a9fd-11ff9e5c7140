import { ChangeDetectorRef, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { RequestSessionService } from '../../../../../services/request-session/request-session.service';
import { RawPayloadType } from '../../../../../model/request';
import { changeOf } from '../../../../_common/util/changes';

@Component({
  selector: 'kng-request-body-raw',
  templateUrl: './request-body-raw.component.html',
  styleUrls: ['./request-body-raw.component.scss'],
  standalone: false,
})
export class RequestBodyRawComponent implements OnChanges {
  @Input() selected = false;

  readonly CodeEditorType = RawPayloadType;

  constructor(
    public requestSessionService: RequestSessionService,
    private readonly changeDetector: ChangeDetectorRef,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (this.selected) {
      const selectedChange = changeOf(changes, (c: this) => c.selected);
      if (selectedChange != null) {
        this.updateContentType();
        this.changeDetector.detectChanges();
      }
    }
  }

  changeEditorType(editorType: RawPayloadType) {
    this.requestSessionService.request.payload.rawPayload.type = editorType;
    this.updateContentType();
  }

  private updateContentType() {
    if (this.selected) {
      this.requestSessionService.setContentType(
        this.getContentType(this.requestSessionService.request.payload.rawPayload.type),
      );
    }
  }

  private getContentType(editorType?: RawPayloadType): string | undefined {
    switch (editorType) {
      case RawPayloadType.JSON:
        return 'application/json';
      case RawPayloadType.XML:
        return 'application/xml';
      default:
        return undefined;
    }
  }

  payloadUpdated(newPayload: string) {
    this.requestSessionService.request.payload.rawPayload.body = newPayload;
  }

  codeEditorBlur(newPayload: string) {
    this.payloadUpdated(newPayload);
    void this.requestSessionService.saveRequest();
  }
}

<div class="request-body-editor">
  <ul class="request-body-editor--type">
    <li>
      <button
        class="link"
        [ngClass]="{
          selected:
            this.requestSessionService.request.payload.rawPayload.type === CodeEditorType.JSON,
        }"
        (click)="changeEditorType(CodeEditorType.JSON)"
      >
        JSON
      </button>
    </li>
    <li>
      <button
        class="link"
        [ngClass]="{
          selected:
            this.requestSessionService.request.payload.rawPayload.type === CodeEditorType.XML,
        }"
        (click)="changeEditorType(CodeEditorType.XML)"
      >
        XML
      </button>
    </li>
    <li>
      <button
        class="link"
        [ngClass]="{
          selected:
            this.requestSessionService.request.payload.rawPayload.type === CodeEditorType.OTHER,
        }"
        (click)="changeEditorType(CodeEditorType.OTHER)"
      >
        Other
      </button>
    </li>
  </ul>
  <kng-code-editor
    class="request-body-editor--editor"
    [value]="this.requestSessionService.request.payload.rawPayload.body"
    [type]="this.requestSessionService.request.payload.rawPayload.type"
    (valueChange)="payloadUpdated($event)"
    (blur)="codeEditorBlur($event)"
  ></kng-code-editor>
</div>

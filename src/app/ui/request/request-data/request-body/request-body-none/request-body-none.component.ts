import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { faFeather } from '@fortawesome/free-solid-svg-icons';
import { RequestSessionService } from '../../../../../services/request-session/request-session.service';
import { changeOf } from '../../../../_common/util/changes';

@Component({
  selector: 'kng-request-body-none',
  templateUrl: './request-body-none.component.html',
  styleUrls: ['./request-body-none.component.scss'],
  standalone: false,
})
export class RequestBodyNoneComponent implements OnInit, OnChanges {
  @Input() selected = false;

  icons = {
    emptyBody: faFeather,
  };

  constructor(
    private readonly requestSessionService: RequestSessionService,
    private readonly changeDetector: ChangeDetectorRef,
  ) {}

  ngOnInit() {
    this.updateRequest();
  }

  ngOnChanges(changes: SimpleChang<PERSON>) {
    const selectedChange = changeOf(changes, (c: this) => c.selected);
    if (selectedChange != null) {
      this.updateRequest();
      this.changeDetector.detectChanges();
    }
  }

  private updateRequest() {
    if (this.selected) {
      this.requestSessionService.setContentType(undefined);
    }
  }
}

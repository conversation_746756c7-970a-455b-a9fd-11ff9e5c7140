<div class="request-form-params">
  @for (
    formParam of requestSessionService.request.payload.formPayload.params;
    track formParam;
    let i = $index
  ) {
    <kng-name-value
      [item]="formParam"
      [focusOnce]="i === focusOnceIndex"
      [currentItem]="currentFormParam"
      nameLabel="Form param name"
      valueLabel="Form param value"
      (select)="selectFormParam(i)"
      (disable)="disableCurrentFormParam()"
      (enable)="enableCurrentFormParam()"
      (delete)="deleteCurrentFormParam()"
      (nameChanged)="nameOrValueChanged()"
      (valueChanged)="nameOrValueChanged()"
    ></kng-name-value>
  }
  <div class="request-form-params--add">
    <button mat-button color="primary" title="Add a new formParam" (click)="addNewFormParam()">
      <fa-icon [icon]="icons.formParamAdd"></fa-icon>
    </button>
  </div>
</div>

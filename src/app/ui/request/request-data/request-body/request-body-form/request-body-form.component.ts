import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { RequestSessionService } from '../../../../../services/request-session/request-session.service';
import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { RequestFormParam } from '../../../../../model/request';
import { changeOf } from '../../../../_common/util/changes';

@Component({
  selector: 'kng-request-body-form',
  templateUrl: './request-body-form.component.html',
  styleUrls: ['./request-body-form.component.scss'],
  standalone: false,
})
export class RequestBodyFormComponent implements OnInit, OnChanges {
  @Input() selected = false;
  private currentFormParamIndex?: number;
  focusOnceIndex?: number;

  readonly icons = {
    formParamAdd: faPlus,
  };

  constructor(
    public readonly requestSessionService: RequestSessionService,
    private readonly changeDetector: ChangeDetectorRef,
  ) {}

  ngOnInit() {
    this.updateContentType();
  }

  ngOnChanges(changes: SimpleChanges) {
    const selectedChange = changeOf(changes, (c: this) => c.selected);
    if (selectedChange != null) {
      this.updateContentType();
      this.changeDetector.detectChanges();
    }
  }

  private updateContentType() {
    if (this.selected) {
      this.requestSessionService.setContentType('application/x-www-form-urlencoded');
    }
  }

  selectFormParam(index: number) {
    this.currentFormParamIndex = index;
  }

  get currentFormParam(): RequestFormParam | undefined {
    if (this.currentFormParamIndex == null) {
      return undefined;
    }
    return this.requestSessionService.request.payload.formPayload.params[
      this.currentFormParamIndex
    ];
  }

  deleteCurrentFormParam() {
    if (this.currentFormParamIndex != null) {
      this.requestSessionService.deleteFormParamAt(this.currentFormParamIndex);
      this.currentFormParamIndex = undefined;
    }
  }

  disableCurrentFormParam() {
    if (this.currentFormParamIndex != null && this.currentFormParam) {
      this.requestSessionService.disableFormParamAt(this.currentFormParamIndex);
      this.currentFormParamIndex = undefined;
    }
  }

  enableCurrentFormParam() {
    if (this.currentFormParamIndex != null && this.currentFormParam) {
      this.requestSessionService.enableFormParamAt(this.currentFormParamIndex);
      this.currentFormParamIndex = undefined;
    }
  }

  addNewFormParam() {
    this.requestSessionService.addFormParam();
    this.focusOnceIndex = this.requestSessionService.request.payload.formPayload.params.length - 1;
  }

  nameOrValueChanged() {
    void this.requestSessionService.saveRequest();
  }
}

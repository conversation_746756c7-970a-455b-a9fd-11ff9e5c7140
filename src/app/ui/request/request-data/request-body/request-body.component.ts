import { Component } from '@angular/core';
import { MatTabChangeEvent } from '@angular/material/tabs';
import { faCircle, faCircleCheck } from '@fortawesome/free-regular-svg-icons';
import { PayloadType } from '../../../../model/request';
import { RequestSessionService } from '../../../../services/request-session/request-session.service';

@Component({
  selector: 'kng-request-body',
  templateUrl: './request-body.component.html',
  styleUrls: ['./request-body.component.scss'],
  standalone: false,
})
export class RequestBodyComponent {
  readonly PayloadType = PayloadType;

  constructor(public readonly requestSessionService: RequestSessionService) {}

  icons = {
    checked: faCircleCheck,
    unChecked: faCircle,
  };

  get selectedTabIndex(): PayloadType {
    return this.requestSessionService.request.payload.type;
  }

  tabChanged(e: MatTabChangeEvent) {
    this.requestSessionService.request.payload.type = e.index;
  }
}

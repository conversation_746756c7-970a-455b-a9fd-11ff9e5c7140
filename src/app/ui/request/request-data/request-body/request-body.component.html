<div class="request-body">
  <div class="request-body--title">Request body</div>
  <mat-tab-group
    dynamicHeight
    preserveContent
    mat-align-tabs="center"
    animationDuration="0ms"
    class="clean"
    [selectedIndex]="selectedTabIndex"
    (selectedTabChange)="tabChanged($event)"
  >
    <mat-tab label="Request body">
      <ng-template mat-tab-label>
        <div class="request-body--tab">
          <div>
            <fa-icon
              [icon]="selectedTabIndex === PayloadType.NONE ? icons.checked : icons.unChecked"
            ></fa-icon>
          </div>
          <div>None</div>
        </div>
      </ng-template>
      <ng-template matTabContent>
        <kng-request-body-none
          [selected]="selectedTabIndex === PayloadType.NONE"
        ></kng-request-body-none>
      </ng-template>
    </mat-tab>
    <mat-tab label="Request body">
      <ng-template mat-tab-label>
        <div class="request-body--tab">
          <div>
            <fa-icon
              [icon]="selectedTabIndex === PayloadType.RAW ? icons.checked : icons.unChecked"
            ></fa-icon>
          </div>
          <div>Raw</div>
        </div>
      </ng-template>
      <ng-template matTabContent>
        <kng-request-body-raw
          [selected]="selectedTabIndex === PayloadType.RAW"
        ></kng-request-body-raw>
      </ng-template>
    </mat-tab>
    <mat-tab>
      <ng-template mat-tab-label>
        <div class="request-body--tab">
          <div>
            <fa-icon
              [icon]="selectedTabIndex === PayloadType.FORM ? icons.checked : icons.unChecked"
            ></fa-icon>
          </div>
          <div>Form</div>
        </div>
      </ng-template>
      <ng-template matTabContent>
        <kng-request-body-form
          [selected]="selectedTabIndex === PayloadType.FORM"
        ></kng-request-body-form>
      </ng-template>
    </mat-tab>
  </mat-tab-group>
</div>

<kng-split-view id="request-data" direction="vertical">
  <kng-split-view-1>
    <mat-tab-group
      dynamicHeight
      preserveContent
      mat-align-tabs="center"
      animationDuration="0ms"
      class="clean"
    >
      <mat-tab>
        <ng-template mat-tab-label>
          Headers
          <span class="badge subtle">
            {{ requestSessionService.request.headers.length }}
          </span>
        </ng-template>
        <ng-template matTabContent>
          <kng-request-headers></kng-request-headers>
        </ng-template>
      </mat-tab>
      <mat-tab>
        <ng-template mat-tab-label>
          Query params
          <span class="badge subtle">
            {{ requestSessionService.request.queryParams.length }}
          </span>
        </ng-template>
        <ng-template matTabContent>
          <kng-request-query-params></kng-request-query-params>
        </ng-template>
      </mat-tab>
    </mat-tab-group>
  </kng-split-view-1>
  <kng-split-view-2>
    <kng-request-body></kng-request-body>
  </kng-split-view-2>
</kng-split-view>

import { Component } from '@angular/core';
import { RequestSessionService } from '../../../services/request-session/request-session.service';
import { SUPPORTED_REQUEST_METHODS } from '../../../model/request';
import { faBracketsCurly } from '@fortawesome/pro-solid-svg-icons';
import { MatDialog } from '@angular/material/dialog';
import { VariablesViewDialogComponent } from '../../variables/variables-view-dialog.component';

@Component({
  selector: 'kng-request-bar',
  templateUrl: './request-bar.component.html',
  styleUrls: ['./request-bar.component.scss'],
  standalone: false,
})
export class RequestBarComponent {
  supportedRequestMethods = SUPPORTED_REQUEST_METHODS;

  icons = {
    var: faBracketsCurly,
  };

  constructor(
    public requestSessionService: RequestSessionService,
    private readonly matDialog: MatDialog,
  ) {}

  methodChanged(newMethod: any) {
    this.requestSessionService.request.method = newMethod;
    void this.requestSessionService.saveRequest();
  }

  async enterPressed(urlBar: HTMLInputElement) {
    this.setUrl(urlBar);
    await this.requestSessionService.performRequest();
  }

  blurred(e: FocusEvent, urlBar: HTMLInputElement) {
    this.setUrl(urlBar);
  }

  urlChanged(urlValueAtEvent: string, urlBar: HTMLInputElement) {
    setTimeout(() => {
      const urlValueNow = urlBar.value;
      if (urlValueAtEvent === urlValueNow && this.requestSessionService.url !== urlValueNow) {
        this.setUrl(urlBar);
      }
    }, 1_000);
  }

  private setUrl(urlBar: HTMLInputElement) {
    this.requestSessionService.url = urlBar.value;
  }

  useVariables() {
    this.matDialog.open(VariablesViewDialogComponent);
  }
}

<div class="request-bar">
  <div class="request-bar--bar">
    <div class="request-bar--bar--method">
      <mat-select
        class="fill"
        [ngModel]="requestSessionService.request.method"
        (valueChange)="methodChanged($event)"
      >
        @for (method of supportedRequestMethods; track method) {
          <mat-option [value]="method">{{ method }}</mat-option>
        }
      </mat-select>
    </div>
    <div class="request-bar--bar--url">
      <input
        #urlBar
        type="text"
        class="primary"
        [ngModel]="requestSessionService.url"
        (blur)="blurred($event, urlBar)"
        placeholder="Request URL..."
        (keydown.enter)="enterPressed(urlBar)"
        (ngModelChange)="urlChanged($event, urlBar)"
        autocomplete="off"
        autocorrect="off"
        autocapitalize="off"
        spellcheck="false"
      />
    </div>
    <div class="request-bar--bar--send-button">
      <button
        mat-flat-button
        color="primary"
        (click)="requestSessionService.performRequest()"
        [disabled]="!requestSessionService.readyToSendRequest"
      >
        Send
      </button>
    </div>
  </div>
  <div class="request-bar--variables">
    <button class="link icon" (click)="useVariables()">
      <fa-icon [icon]="icons.var"></fa-icon>
      <span>Use variables</span>
    </button>
  </div>
</div>

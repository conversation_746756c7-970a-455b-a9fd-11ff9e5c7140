@use "src/styles/colors" as *;
@use "src/styles/typography" as *;

.request-bar {
  padding: 1rem;
  @include a-bk-color($color-primary-slighter);
  &--bar {
    display: flex;
    &--method {
      width: 8rem;
    }
    &--url {
      flex-grow: 1;
      input {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0 1rem;
        font-weight: bold;
      }
    }
    &--send-button {
      width: 8rem;
      button {
        width: 100%;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }

  &--variables {
    button {
      font-size: $font-size-small;
      margin-left: 9rem;
      //display: flex;
      //flex-direction: row;
      //justify-content: center;
      //align-items: center;
      padding-top: 0.5rem;
      //gap: 0.25rem;
    }
  }
}

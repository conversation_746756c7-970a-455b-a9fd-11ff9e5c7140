@use "src/styles/colors" as *;

$divider-size: 0.25rem;
$divider-highlight-size: 0.25rem;
$panel-min-size: 10rem;

.split-view {
  display: flex;
  width: 100%;
  height: 100%;
  &--1 {
    overflow: auto;
  }
  &--divider {
    display: flex;
    justify-content: center;
    @include a-bk-color($color-struct-border);
    &--highlight {
      @include a-bk-color($color-struct-border);
    }
    &.dragging {
      @include a-bk-color($color-struct-border);

      .split-view--divider--highlight {
        @include a-bk-color($color-primary);
      }
    }
  }
  &--2 {
    overflow: auto;
  }

  &.horizontal {
    flex-direction: row;
    width: calc(100% - $divider-size);
    & > .split-view--1 {
      width: 50%;
      min-width: $panel-min-size;
    }
    & > .split-view--divider {
      flex-direction: row;
      min-width: $divider-size;
      max-width: $divider-size;
      width: $divider-size;
      cursor: ew-resize;
      .split-view--divider--highlight {
        width: $divider-highlight-size;
        min-width: $divider-highlight-size;
        max-width: $divider-highlight-size;
      }
    }
    & > .split-view--2 {
      width: 50%;
      min-width: $panel-min-size;
    }
  }

  &.vertical {
    flex-direction: column;
    height: calc(100% - $divider-size);
    & > .split-view--1 {
      height: 50%;
      min-height: $panel-min-size;
    }

    & > .split-view--divider {
      flex-direction: column;
      min-height: $divider-size;
      max-height: $divider-size;
      height: $divider-size;
      cursor: ns-resize;
      .split-view--divider--highlight {
        height: $divider-highlight-size;
        min-height: $divider-highlight-size;
        max-height: $divider-highlight-size;
      }
    }
    & > .split-view--2 {
      height: 50%;
      min-height: $panel-min-size;
    }
  }
}

<div
  class="split-view"
  [ngClass]="{
    vertical: vertical,
    horizontal: horizontal,
  }"
>
  @if (pane1 != null) {
    <div
      class="split-view--1"
      [ngStyle]="{
        width: horizontal ? panel1Size + '%' : 'inherit',
        'min-width': horizontal ? panel1Size + '%' : 'inherit',
        'max-width': horizontal ? panel1Size + '%' : 'inherit',
        height: vertical ? panel1Size + '%' : 'inherit',
        'min-height': vertical ? panel1Size + '%' : 'inherit',
        'max-height': vertical ? panel1Size + '%' : 'inherit',
      }"
    >
      <ng-content select="kng-split-view-1"></ng-content>
    </div>
  }
  @if (hasPanes) {
    <div
      class="split-view--divider"
      cdkDrag
      [cdkDragLockAxis]="horizontal ? 'x' : 'y'"
      (cdkDragMoved)="splitterMoved($event)"
      (cdkDragStarted)="splitterStartDragging()"
      (cdkDragEnded)="splitterEndDragging()"
      (dblclick)="resetSplitterPosition()"
      [ngClass]="{
        dragging: draggingSplitter,
      }"
      [title]="panel1Size != 50 ? 'Double click to reset position' : ''"
    >
      <div class="split-view--divider--highlight"></div>
    </div>
  }
  @if (pane2 != null) {
    <div
      class="split-view--2"
      [ngStyle]="{
        width: horizontal ? panel2Size + '%' : 'inherit',
        'min-width': horizontal ? panel2Size + '%' : 'inherit',
        'max-width': horizontal ? panel2Size + '%' : 'inherit',
        height: vertical ? panel2Size + '%' : 'inherit',
        'min-height': vertical ? panel2Size + '%' : 'inherit',
        'max-height': vertical ? panel2Size + '%' : 'inherit',
      }"
    >
      <ng-content select="kng-split-view-2"></ng-content>
    </div>
  }
</div>

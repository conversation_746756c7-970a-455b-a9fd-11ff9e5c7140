import { NgModule } from '@angular/core';
import {
  SplitViewComponent,
  SplitViewPane1Directive,
  SplitViewPane2Directive,
} from './split-view.component';
import { NgClass, NgIf, NgStyle } from '@angular/common';
import { DragDropModule } from '@angular/cdk/drag-drop';

@NgModule({
  declarations: [SplitViewComponent, SplitViewPane1Directive, SplitViewPane2Directive],
  imports: [NgStyle, DragDropModule, NgClass, NgIf],
  providers: [],
  bootstrap: [],
  exports: [SplitViewComponent, SplitViewPane1Directive, SplitViewPane2Directive],
})
export class SplitViewModule {}

import { Component, ContentChild, Directive, Input, OnChanges, SimpleChanges } from '@angular/core';
import { CdkDragMove } from '@angular/cdk/drag-drop';
import { LayoutService } from '../../../services/system/layout.service';
import { changeOf } from '../util/changes';

@Directive({
  selector: 'kng-split-view-1',
  standalone: false,
})
export class SplitViewPane1Directive {}

@Directive({
  selector: 'kng-split-view-2',
  standalone: false,
})
export class SplitViewPane2Directive {}

@Component({
  selector: 'kng-split-view',
  templateUrl: './split-view.component.html',
  styleUrls: ['./split-view.component.scss'],
  standalone: false,
})
export class SplitViewComponent implements OnChanges {
  #panel1Size: number = 50;
  #panel2Size: number = 50;
  draggingSplitter = false;

  @Input() id?: string;
  @Input() refreshOnResize = false;
  @Input() direction: 'horizontal' | 'vertical' = 'horizontal';
  @Input() splitLimit = 200;

  @ContentChild(SplitViewPane1Directive) pane1?: SplitViewPane1Directive;
  @ContentChild(SplitViewPane2Directive) pane2?: SplitViewPane2Directive;

  constructor(private readonly layoutService: LayoutService) {}

  @Input()
  set ratio(ratio: number) {
    if (ratio != null) {
      this.#panel1Size = ratio * 100;
      this.#panel2Size = 100 - this.#panel1Size;
    }
  }

  get panel1Size(): number {
    if (this.pane1 == null) {
      return 0;
    }
    if (this.pane2 == null) {
      return 100;
    }
    return this.#panel1Size;
  }

  get panel2Size(): number {
    if (this.pane2 == null) {
      return 0;
    }
    if (this.pane1 == null) {
      return 100;
    }
    return this.#panel2Size;
  }

  get hasPanes(): boolean {
    return this.pane1 != null && this.pane2 != null;
  }

  async ngOnChanges(changes: SimpleChanges) {
    const idChange = changeOf(changes, (c: this) => c.id);
    if (this.id != null && idChange.isFirstChange()) {
      const ratio = await this.layoutService.getRatio(this.id);
      if (ratio != null) {
        this.ratio = ratio;
      }
    }
  }

  splitterMoved(event: CdkDragMove<string>) {
    const pointerPosition = this.horizontal ? event.pointerPosition.x : event.pointerPosition.y;

    const nativeEl = event.source.element.nativeElement;
    const parentEl = nativeEl.parentElement!;
    const parentRect = parentEl.getBoundingClientRect();
    const parentPosition = this.horizontal ? parentRect.x : parentRect.y;
    const parentSize = this.horizontal ? parentRect.width : parentRect.height;

    let newSplitDistance = pointerPosition - parentPosition;
    if (newSplitDistance < this.splitLimit) {
      newSplitDistance = this.splitLimit;
    }
    if (newSplitDistance > parentSize - this.splitLimit) {
      newSplitDistance = parentSize - this.splitLimit;
    }
    nativeEl.style.transform = 'none';

    this.#panel1Size = Math.round(100 * newSplitDistance) / parentSize;
    this.#panel2Size = 100 - this.#panel1Size;

    this.triggerWindowResize();
  }

  get vertical(): boolean {
    return this.direction === 'vertical';
  }

  get horizontal(): boolean {
    return this.direction === 'horizontal';
  }

  async resetSplitterPosition() {
    this.#panel1Size = 50;
    this.#panel2Size = 50;
    await this.persistRatio();
  }

  splitterStartDragging() {
    this.draggingSplitter = true;
  }

  async splitterEndDragging() {
    this.draggingSplitter = false;
    console.debug('Splitter moved', this.panel1Size / 100);
    await this.persistRatio();
  }

  private triggerWindowResize() {
    /*
      This triggers a window resize event so that some
      components get a chance to adapt to the new size.

      An example of such component is the Angular Material
      Tab that decides whether to display the tab scrollers
      every time the window is resized.
     */
    if (this.refreshOnResize) {
      window.dispatchEvent(
        new CustomEvent('resize', {
          bubbles: false,
        }),
      );
    }
  }

  private async persistRatio() {
    if (this.id != null) {
      await this.layoutService.persistRatio(this.id, this.panel1Size / 100);
    }
  }
}

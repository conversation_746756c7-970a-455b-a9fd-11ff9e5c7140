import {
  AfterViewInit,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { basicSetup, EditorView } from 'codemirror';
import { EditorState, Extension } from '@codemirror/state';
import { json } from '@codemirror/lang-json';
import { xml } from '@codemirror/lang-xml';
import { RawPayloadType } from '../../../model/request';
import { changeOf } from '../util/changes';

@Component({
  selector: 'kng-code-editor',
  templateUrl: './code-editor.component.html',
  styleUrls: ['./code-editor.component.scss'],
  standalone: false,
})
export class CodeEditorComponent implements AfterViewInit, OnChanges {
  @Input() value?: string;
  @Input() readonly = false;
  @Input() type = RawPayloadType.JSON;
  @Output() valueChange = new EventEmitter<string>();
  @Output() blur = new EventEmitter<string>();
  @ViewChild('editor', { static: true }) editor?: ElementRef;

  #editorView?: EditorView;

  ngAfterViewInit(): void {
    this.#editorView = new EditorView({
      state: this.createEditorState(),
      parent: this.editor?.nativeElement,
    });
    this.#editorView.contentDOM.onblur = () => {
      this.blur.emit(this.#editorView?.state?.doc?.toString());
    };
  }

  ngOnChanges(changes: SimpleChanges): void {
    const typeChange = changeOf(changes, (c: this) => c.type);
    const readonlyChange = changeOf(changes, (c: this) => c.readonly);
    if (typeChange != null || readonlyChange != null) {
      this.#editorView?.setState(this.createEditorState());
    }
    const valueChange = changeOf(changes, (c: this) => c.value);
    if (valueChange != null) {
      const existingValue = this.#editorView?.state?.doc?.toString();
      const newValue = valueChange.currentValue;
      if (existingValue !== newValue) {
        this.#editorView?.setState(this.createEditorState());
      }
    }
  }

  private createEditorState() {
    return EditorState.create({
      doc: this.value ?? '',
      extensions: [
        basicSetup,
        EditorView.editable.of(!this.readonly),
        ...this.determineEditorTypeExtensions(),
        EditorView.updateListener.of((e) => {
          this.valueUpdated(e.state.doc.toString());
        }),
      ],
    });
  }

  private valueUpdated(newValue: string) {
    this.valueChange.emit(newValue);
  }

  private determineEditorTypeExtensions(): Extension[] {
    switch (this.type) {
      case RawPayloadType.JSON:
        return [json()];
      case RawPayloadType.XML:
        return [xml()];
      default:
        return [];
    }
  }
}

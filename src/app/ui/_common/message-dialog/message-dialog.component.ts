import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { faCircleInfo, faCircleQuestion, faCircleXmark } from '@fortawesome/free-solid-svg-icons';
import { UserMessage } from '../../../../common/system';

@Component({
  selector: 'kng-message-dialog',
  templateUrl: './message-dialog.component.html',
  styleUrls: ['./message-dialog.component.scss'],
  standalone: false,
})
export class MessageDialogComponent {
  faCircleQuestion = faCircleQuestion;
  faCircleXmark = faCircleXmark;
  iconInfo = faCircleInfo;

  constructor(
    private readonly dialogRef: MatDialogRef<MessageDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public readonly data: UserMessage,
  ) {}

  confirm() {
    this.dialogRef.close(true);
  }

  cancel() {
    if (this.cancelIsPositive()) {
      this.dialogRef.close(true);
    } else {
      this.dialogRef.close(false);
    }
  }

  private cancelIsPositive() {
    return this.data.confirm == null;
  }
}

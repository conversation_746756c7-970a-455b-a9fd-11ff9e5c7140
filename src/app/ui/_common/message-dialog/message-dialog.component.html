<div class="message-dialog">
  <div class="message-dialog--title" mat-dialog-title>
    @if (data.messageType === "info") {
      <div class="message-dialog--title--icon info">
        <fa-icon [icon]="iconInfo"></fa-icon>
      </div>
    }
    @if (data.messageType === "question") {
      <div class="message-dialog--title--icon question">
        <fa-icon [icon]="faCircleQuestion"></fa-icon>
      </div>
    }
    @if (data.messageType === "error") {
      <div class="message-dialog--title--icon error">
        <fa-icon [icon]="faCircleXmark"></fa-icon>
      </div>
    }
  </div>
  <div class="message-dialog--message" mat-dialog-content>
    @if (data.title) {
      <p class="message-dialog--message--title">
        {{ data.title }}
      </p>
    }
    <p class="message-dialog--message--message">{{ data.message }}</p>
    @if (data.detail) {
      <p class="message-dialog--message--detail">
        {{ data.detail }}
      </p>
    }
  </div>
  <div mat-dialog-actions>
    @if (data.confirm != null) {
      <button
        class="message-dialog--action-button"
        mat-button
        color="primary"
        (click)="confirm()"
        cdkFocusInitial
      >
        {{ data.confirm }}
      </button>
    }
    <button class="message-dialog--cancel-button" mat-button (click)="cancel()">
      {{ data.cancel }}
    </button>
  </div>
</div>

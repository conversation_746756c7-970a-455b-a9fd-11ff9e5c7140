import { NgModule } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/common';
import { TreeComponent } from './tree.component';
import { MatMenuModule } from '@angular/material/menu';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { MatDividerModule } from '@angular/material/divider';

@NgModule({
  declarations: [TreeComponent],
  imports: [
    FontAwesomeModule,
    NgIf,
    NgFor,
    NgStyle,
    NgClass,
    MatMenuModule,
    DragDropModule,
    AsyncPipe,
    MatDividerModule,
  ],
  providers: [],
  bootstrap: [],
  exports: [TreeComponent],
})
export class TreeModule {}

import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { TreeDataSource, TreeDelegate, TreeNode } from './tree-node';
import { CdkDrag, CdkDragDrop } from '@angular/cdk/drag-drop';
import { faChevronDown, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { MatMenuTrigger } from '@angular/material/menu';

@Component({
  selector: 'kng-tree',
  templateUrl: './tree.component.html',
  styleUrls: ['./tree.component.scss'],
  standalone: false,
})
export class TreeComponent implements OnInit {
  @Input() dataSource?: TreeDataSource;
  @Input() delegate?: TreeDelegate;
  menuPosition = { top: 0, left: 0 };

  @ViewChild(MatMenuTrigger) trigger?: MatMenuTrigger;
  icons = {
    nodeCollapsed: faChevronRight,
    nodeExpanded: faChevronDown,
  };

  ngOnInit() {
    if (this.dataSource == null) {
      console.error('Tree dataSource is required.');
    }
  }

  toggleExpand(node: TreeNode) {
    if (this.dataSource?.isLeaf(node) === false) {
      if (this.dataSource?.isExpanded(node) === true) {
        this.dataSource?.collapse(node);
      } else {
        this.dataSource?.expand(node);
      }
    }
  }

  selectNode(node: TreeNode) {
    if (this.dataSource?.allowsSelection(node) === true) {
      this.dataSource.select(node);
    }
  }

  showContextMenu(event: MouseEvent, node: TreeNode) {
    if (this.delegate != null) {
      const contextActions = this.delegate.contextActions(node);
      if (contextActions.length > 0) {
        this.menuPosition = {
          top: event.y,
          left: event.x,
        };
        this.trigger!.menuData = { node, contextActions };
        this.trigger!.openMenu();
        return false;
      }
    }
    return true;
  }

  allowDrop = (rawIndex: number, item: CdkDrag<TreeNode>) => {
    let debug: any = {
      node: item.data.label,
      rawIndex,
    };
    function printDebug(reason: string, fn: () => boolean): boolean {
      const match = fn();
      if (match) {
        console.debug(JSON.stringify({ ...debug, match, reason }, null, 2));
      } else {
        console.warn(JSON.stringify({ ...debug, match, reason }, null, 2));
      }
      return match;
    }

    if (this.delegate == null) {
      return printDebug('No delegate', () => false);
    }

    const sourceIndex = this.dataSource?.indexOf(item.data);
    if (sourceIndex == null) {
      return printDebug('sourceIndex == null', () => false);
    }

    if (sourceIndex === rawIndex) {
      return printDebug('Same index', () => true);
    }

    const destIndex = (() => {
      let destIndex: number;
      if (rawIndex < sourceIndex) {
        destIndex = rawIndex - 1;
      } else {
        destIndex = rawIndex;
      }
      debug = { ...debug, destIndex };
      return destIndex;
    })();

    const destinationNode = this.dataSource?.treeNodes[destIndex];
    debug = { ...debug, destination: destinationNode?.label };
    if (destinationNode == null) {
      return printDebug('destinationNode == null (root)', () =>
        this.delegate!.allowsDropAsRoot(item.data),
      );
    } else {
      if (this.dataSource?.isExpanded(destinationNode)) {
        return printDebug(`destinationNode (${destinationNode.label}) expanded`, () =>
          this.delegate!.allowsDropInto(item.data, destinationNode),
        );
      } else {
        const parentNode = this.dataSource?.parentOf(destinationNode);
        if (parentNode != null) {
          return printDebug(`parentNode ${parentNode.label}`, () =>
            this.delegate!.allowsDropInto(item.data, parentNode),
          );
        } else {
          return printDebug('parentNode == null (root)', () =>
            this.delegate!.allowsDropAsRoot(item.data),
          );
        }
      }
    }
  };

  async drop(event: CdkDragDrop<TreeNode, any>) {
    const node: TreeNode = event.item.data;
    if (event.previousIndex === event.currentIndex) {
      return;
    }

    if (this.delegate == null) {
      return;
    }

    const destIndex =
      event.currentIndex < event.previousIndex ? event.currentIndex - 1 : event.currentIndex;
    const treeNodes = this.dataSource?.treeNodes;
    const destinationNode = treeNodes?.[destIndex];

    if (destinationNode == null) {
      await this.delegate.move(node, 0);
    } else if (this.dataSource?.isExpanded(destinationNode)) {
      await this.delegate.move(node, 0, destinationNode);
    } else {
      const parentNode = this.dataSource?.parentOf(destinationNode);
      const destinationIndexAtParent = this.dataSource?.indexAtParent(destinationNode, node) ?? 0;
      await this.delegate.move(node, destinationIndexAtParent + 1, parentNode);
    }
  }

  async editFieldKeyDown(e: KeyboardEvent, node: TreeNode, el: HTMLInputElement) {
    if (e.key == 'Enter') {
      await this.commitEditing(node, el);
    } else if (e.key == 'Escape') {
      this.cancelEditing(node, el);
    }
  }

  async commitEditing(node: TreeNode, el: HTMLInputElement) {
    if (this.dataSource?.isRenaming(node)) {
      const newName = el.value;
      await this.dataSource?.rename(node, newName);
    }
  }

  private cancelEditing(node: TreeNode, el: HTMLInputElement) {
    if (this.dataSource?.isRenaming(node)) {
      el.value = node.label;
      this.dataSource?.cancelRename(node);
    }
  }

  renameNode(node: TreeNode) {
    if (this.dataSource?.allowsRename(node) === true) {
      this.dataSource?.beginRename(node);
    }
  }
}

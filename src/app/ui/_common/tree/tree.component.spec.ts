import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TreeComponent } from './tree.component';
import { MatMenuModule } from '@angular/material/menu';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { ProjectTreeTestUtils } from '../../../../test/project-tree.test.util';
import { ProjectsTreeService } from '../../navigator/projects/projects-tree.service';
import { ProjectsService } from '../../../services/projects/projects.service';
import { StateService } from '../../../services/state/state.service';
import { Project, ProjectItemType } from '../../../model/project';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FontAwesomeTestingModule } from '@fortawesome/angular-fontawesome/testing';
import { ProjectState } from '../../../model/state';
import nodeOfRequest = ProjectTreeTestUtils.nodeOfRequest;
import request = ProjectTreeTestUtils.request;
import folder = ProjectTreeTestUtils.folder;
import project = ProjectTreeTestUtils.project;
import nodeIdFolder = ProjectTreeTestUtils.nodeIdFolder;
import nodeIdProject = ProjectTreeTestUtils.nodeIdProject;
import nodeOfProject = ProjectTreeTestUtils.nodeOfProject;
import { UserMessageService } from '../../../services/system/user-message.service';
import { RequestSessionService } from '../../../services/request-session/request-session.service';

describe('TreeComponent', () => {
  let component: TreeComponent;
  let fixture: ComponentFixture<TreeComponent>;
  let stateService: StateService;
  let projectsService: ProjectsService;
  let requestSessionService: RequestSessionService;
  let userMessageService: UserMessageService;
  let service: ProjectsTreeService;

  async function setUp(testData: { projects?: Project[]; state?: ProjectState } = {}) {
    stateService = jasmine.createSpyObj<StateService>(
      'StateService',
      {
        ['loadProjectsState']: Promise.resolve(),
        ['updateItemState']: undefined,
        ['updateSelection']: undefined,
        ['cleanUpProjectState']: undefined,
      },
      {
        ['state']: testData.state ?? { itemState: {} },
      },
    );
    projectsService = jasmine.createSpyObj<ProjectsService>(
      'ProjectsService',
      {
        ['loadProjects']: Promise.resolve(),
        ['move']: Promise.resolve(),
        ['allIds']: [],
      },
      { ['projects']: testData.projects ?? [] },
    );
    requestSessionService = jasmine.createSpyObj<RequestSessionService>('RequestSessionService', {
      ['select']: undefined,
    });
    userMessageService = jasmine.createSpyObj<UserMessageService>('UserMessageService', {
      ['confirm']: Promise.resolve(),
    });
    service = new ProjectsTreeService(
      projectsService,
      stateService,
      requestSessionService,
      userMessageService,
    );

    await TestBed.configureTestingModule({
      declarations: [TreeComponent],
      imports: [MatMenuModule, DragDropModule, FontAwesomeModule, FontAwesomeTestingModule],
    }).compileComponents();

    fixture = TestBed.createComponent(TreeComponent);
    component = fixture.componentInstance;
    await service.initialise();
    component.dataSource = service;
    component.delegate = service;

    fixture.detectChanges();
  }

  describe('TreeComponent move request across', () => {
    const request1 = request('r1');
    const request2 = request('r2');
    const request3 = request('r3');
    const request4 = request('r4');
    const folder1 = folder('f1').withRequest(request1).withRequest(request2).withRequest(request3);
    const project1 = project('p1').withRequest(request4).withFolder(folder1);
    const request5 = request('r5');
    const request6 = request('r6');
    const folder2 = folder('f2').withRequest(request6);
    const folder3 = folder('f3');
    const project2 = project('p2').withRequest(request5).withFolder(folder2).withFolder(folder3);

    const projects = [project1, project2];
    const state = {
      itemState: {
        [nodeIdProject(project1)]: { expanded: true },
        [nodeIdProject(project2)]: { expanded: true },
        [nodeIdFolder(folder1)]: { expanded: true },
        [nodeIdFolder(folder3)]: { expanded: true },
      },
    };

    beforeEach(async () => {
      await setUp({ projects, state });
    });

    it('should drop request into another project', async () => {
      await component.drop({
        item: { data: nodeOfRequest(request2, { level: 2 }) },
        previousIndex: 4,
        currentIndex: 8,
      } as any);

      expect(projectsService.move).toHaveBeenCalledOnceWith(
        { type: ProjectItemType.REQUEST, value: request2 },
        2,
        project2,
      );
    });

    it("should drop request into another project's folder", async () => {
      await component.drop({
        item: { data: nodeOfRequest(request2, { level: 2 }) },
        previousIndex: 4,
        currentIndex: 9,
      } as any);

      expect(projectsService.move).toHaveBeenCalledOnceWith(
        { type: ProjectItemType.REQUEST, value: request2 },
        0,
        { type: ProjectItemType.FOLDER, value: folder3 },
      );
    });
  });

  describe('TreeComponent move request outside', () => {
    const request1 = request('r1');
    const request2 = request('r2');
    const request3 = request('r3');
    const request4 = request('r4');
    const folder1 = folder('f1').withRequest(request1).withRequest(request2).withRequest(request3);
    const project1 = project('p1').withFolder(folder1).withRequest(request4);

    const projects = [project1];
    const state = {
      itemState: {
        [nodeIdProject(project1)]: { expanded: true },
        [nodeIdFolder(folder1)]: { expanded: true },
      },
    };

    beforeEach(async () => {
      await setUp({ projects, state });
    });

    it('should drop request outside and above its still expanded folder', async () => {
      await component.drop({
        item: { data: nodeOfRequest(request3, { level: 2 }) },
        previousIndex: 4,
        currentIndex: 1,
      } as any);

      expect(projectsService.move).toHaveBeenCalledOnceWith(
        { type: ProjectItemType.REQUEST, value: request3 },
        0,
        project1,
      );
    });

    it('should drop request outside and below its still expanded folder', async () => {
      await component.drop({
        item: { data: nodeOfRequest(request3, { level: 2 }) },
        previousIndex: 4,
        currentIndex: 5,
      } as any);

      expect(projectsService.move).toHaveBeenCalledOnceWith(
        { type: ProjectItemType.REQUEST, value: request3 },
        2,
        project1,
      );
    });
  });

  describe('TreeComponent projects', () => {
    const request1 = request('r1');
    const folder1 = folder('f1').withRequest(request1);
    const project1 = project('p1').withFolder(folder1);
    const project2 = project('p2');
    const project3 = project('p3');
    const project4 = project('p4');

    const projects = [project1, project2, project3, project4];
    const state = {
      itemState: {
        [nodeIdProject(project1)]: { expanded: true },
        [nodeIdFolder(folder1)]: { expanded: true },
      },
    };

    beforeEach(async () => {
      console.info(JSON.stringify(projects));
      await setUp({ projects, state });
    });

    it('should move project to top', async () => {
      await component.drop({
        item: { data: nodeOfProject(project4) },
        previousIndex: 5,
        currentIndex: 0,
      } as any);

      expect(projectsService.move).toHaveBeenCalledOnceWith(project4, 0, undefined);
    });

    it('should move project up', async () => {
      await component.drop({
        item: { data: nodeOfProject(project4) },
        previousIndex: 5,
        currentIndex: 4,
      } as any);

      expect(projectsService.move).toHaveBeenCalledOnceWith(project4, 2, undefined);
    });

    it('should move project down', async () => {
      await component.drop({
        item: { data: nodeOfProject(project1) },
        previousIndex: 0,
        currentIndex: 4,
      } as any);

      expect(projectsService.move).toHaveBeenCalledOnceWith(project1, 2, undefined);
    });
  });
});

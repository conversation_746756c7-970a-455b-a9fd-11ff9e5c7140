import { IconProp } from '@fortawesome/fontawesome-svg-core';
import { faHorizontalRule } from '@fortawesome/pro-regular-svg-icons';

export type TreeNode = {
  id: string;
  label: string;
  extraInfo?: { info: string; style: string }[];
  icon: IconProp;
  level: number;
  data: any;
  type: any;
};

export type TreeNodeContextAction = {
  label: string;
  icon: IconProp;
  action: (node: TreeNode) => void;
  negative: boolean;
  disabled?: boolean;
};

export function treeNodeContextActionSeparator(): TreeNodeContextAction {
  return {
    label: '-',
    action: () => {},
    icon: faHorizontalRule,
    negative: false,
  };
}

export interface TreeDataSource {
  get treeNodes(): Readonly<TreeNode[]>;

  isLeaf(node: TreeNode): boolean;
  isExpanded(node: TreeNode): boolean;
  expand(node: TreeNode): Promise<void>;
  collapse(node: TreeNode): Promise<void>;
  children(node: TreeNode): Promise<TreeNode[]>;
  parentOf(node: TreeNode): TreeNode | undefined;
  indexOf(node: TreeNode): number | undefined;
  indexAtParent(node: TreeNode, skippingNode?: TreeNode): number | undefined;

  allowsSelection(node: TreeNode): boolean;
  isSelected(node: TreeNode): boolean;
  select(node: TreeNode): void;

  allowsRename(node: TreeNode): boolean;
  beginRename(node: TreeNode): void;
  isRenaming(node: TreeNode): boolean;
  cancelRename(node: TreeNode): void;

  rename(node: TreeNode, newName: string): Promise<void>;
  delete(node: TreeNode): Promise<void>;
}

export interface TreeDelegate {
  contextActions(node: TreeNode): TreeNodeContextAction[];

  allowsDrag(node: TreeNode): boolean;
  allowsDropAsRoot(node: TreeNode): boolean;
  allowsDropInto(node: TreeNode, parent: TreeNode): boolean;

  move(node: TreeNode, newIndex: number, newParent?: TreeNode): Promise<void>;
}

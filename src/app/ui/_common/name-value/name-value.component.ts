import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { faEllipsisVertical } from '@fortawesome/free-solid-svg-icons';
import { faEye, faEyeSlash, faTrashCan } from '@fortawesome/free-regular-svg-icons';
import { focus } from '../util/focus';
import { changeOf } from '../util/changes';

@Component({
  selector: 'kng-name-value',
  templateUrl: './name-value.component.html',
  styleUrls: ['./name-value.component.scss'],
  standalone: false,
})
export class NameValueComponent implements OnChanges {
  @Input() item?: NameValueItem;
  @Input() focusOnce?: boolean;
  @Input() currentItem?: NameValueItem;
  @Input() nameLabel: string = 'Name';
  @Input() valueLabel: string = 'Value';
  @Output() nameChanged = new EventEmitter<void>();
  @Output() valueChanged = new EventEmitter<void>();
  @Output() select = new EventEmitter<void>();
  @Output() disable = new EventEmitter<void>();
  @Output() enable = new EventEmitter<void>();
  @Output() delete = new EventEmitter<void>();

  #originalName: string | undefined;
  #originalValue: string | undefined;

  @ViewChild('nameRef') nameRef?: ElementRef;

  readonly icons = {
    itemMenu: faEllipsisVertical,
    itemDelete: faTrashCan,
    itemDisable: faEyeSlash,
    itemEnable: faEye,
  };

  ngOnChanges(changes: SimpleChanges): void {
    const itemChange = changeOf(changes, (c: this) => c.item);
    if (itemChange) {
      this.#originalName = itemChange.currentValue?.name;
      this.#originalValue = itemChange.currentValue?.value;
    }

    const focusOnceChanges = changeOf(changes, (t: NameValueComponent) => t.focusOnce);
    if (
      focusOnceChanges &&
      focusOnceChanges.currentValue === true &&
      !focusOnceChanges.previousValue
    ) {
      focus(() => this.nameRef);
    }
  }

  nameBlurred() {
    if (this.item != null) {
      if (this.item.name != this.#originalName) {
        this.#originalName = this.item.name;
        this.nameChanged.emit();
      }
    }
  }

  valueBlurred() {
    if (this.item != null) {
      if (this.item.value != this.#originalValue) {
        this.#originalValue = this.item.value;
        this.valueChanged.emit();
      }
    }
  }
}

export interface NameValueItem {
  name: string;
  value?: string;
  disabled?: boolean;
}

@if (item) {
  <div
    class="name-value"
    [ngClass]="{
      disabled: item.disabled,
    }"
  >
    <div class="name-value--name">
      <input
        #nameRef
        type="text"
        [(ngModel)]="item.name"
        [placeholder]="nameLabel"
        (blur)="nameBlurred()"
        autocomplete="off"
        autocorrect="off"
        autocapitalize="off"
        spellcheck="false"
      />
    </div>
    <div class="name-value--menu">
      <button [matMenuTriggerFor]="headerMenu" (click)="select.emit()" tabindex="-1">
        <fa-icon [icon]="icons.itemMenu"></fa-icon>
      </button>
    </div>
    <div class="name-value--value">
      <input
        type="text"
        [(ngModel)]="item.value"
        [placeholder]="valueLabel"
        (blur)="valueBlurred()"
        autocomplete="off"
        autocorrect="off"
        autocapitalize="off"
        spellcheck="false"
      />
    </div>
    <mat-menu #headerMenu="matMenu">
      @if (currentItem && !currentItem.disabled) {
        <button mat-menu-item (click)="disable.emit()">
          <fa-icon [icon]="icons.itemDisable"></fa-icon>
          Disable
        </button>
      }
      @if (currentItem?.disabled === true) {
        <button mat-menu-item (click)="enable.emit()">
          <fa-icon [icon]="icons.itemEnable"></fa-icon>
          Enable
        </button>
      }
      @if (currentItem) {
        <button mat-menu-item (click)="delete.emit()" class="negative">
          <fa-icon [icon]="icons.itemDelete"></fa-icon>
          Delete
        </button>
      }
    </mat-menu>
  </div>
}

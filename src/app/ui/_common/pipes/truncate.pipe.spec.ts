import { TruncatePipe } from './truncate.pipe';

describe('TruncatePipe', () => {
  it('doesnt truncate null', () => {
    const pipe = new TruncatePipe();
    expect(pipe.transform(null)).toBe('');
    expect(pipe.transform(undefined)).toBe('');
  });

  it('truncates at over 10 by default', () => {
    const pipe = new TruncatePipe();
    expect(pipe.transform('123456789012345')).toBe('1234567890…');
    expect(pipe.transform('12345678901')).toBe('1234567890…');
    expect(pipe.transform('1234567890')).toBe('1234567890');
    expect(pipe.transform('123456')).toBe('123456');
    expect(pipe.transform('å∫č∂ēƒ©˙į∆˚')).toBe('å∫č∂ēƒ©˙į∆…');
  });

  it('truncates at specified limit', () => {
    const pipe = new TruncatePipe();
    expect(pipe.transform('123456789012345', 9)).toBe('123456789…');
    expect(pipe.transform('123456789012', 11)).toBe('12345678901…');
    expect(pipe.transform('å∫č∂ēƒ©˙į∆˚', 3)).toBe('å∫č…');
  });

  it('does truncate at 10 or below', () => {
    const pipe = new TruncatePipe();
    expect(pipe.transform('1234567890')).toBe('1234567890');
    expect(pipe.transform('123456')).toBe('123456');
    expect(pipe.transform('')).toBe('');
    expect(pipe.transform('å∫č∂ēƒ©˙į∆')).toBe('å∫č∂ēƒ©˙į∆');
  });

  it('doesnt truncate at specified limit or below', () => {
    const pipe = new TruncatePipe();
    expect(pipe.transform('123456789', 9)).toBe('123456789');
    expect(pipe.transform('123456', 8)).toBe('123456');
    expect(pipe.transform('', 0)).toBe('');
    expect(pipe.transform('å∫č∂ēƒ©˙į∆żñà', 13)).toBe('å∫č∂ēƒ©˙į∆żñà');
  });
});

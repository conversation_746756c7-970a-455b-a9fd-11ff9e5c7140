import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'truncate',
  standalone: false,
})
export class TruncatePipe implements PipeTransform {
  transform(value?: any, args?: any): any {
    if (value == null) {
      return '';
    }

    const val = value.toString();
    const limit = args != null && typeof args === 'number' ? (args as number) : 10;

    if (val.length <= limit) {
      return val;
    } else {
      return `${val.substring(0, limit)}…`;
    }
  }
}

import { ElementRef } from '@angular/core';

function getHtmlElement(element: Element): HTMLElement | null | undefined {
  let htmlElement: HTMLElement | null | undefined = null;
  if (typeof element === 'string') {
    htmlElement = document.getElementById(element);
  } else if (element instanceof ElementRef) {
    htmlElement = element.nativeElement;
  } else if (element instanceof HTMLElement) {
    htmlElement = element;
  }
  return htmlElement;
}

export type Element = ElementRef | HTMLInputElement | HTMLElement | string | undefined | null;

export function elementDoLater(element: () => Element, action: (htmlElement: HTMLElement) => void) {
  setTimeout(() => {
    const htmlElement = getHtmlElement(element());
    if (htmlElement) {
      action(htmlElement);
    }
  }, 250);
}

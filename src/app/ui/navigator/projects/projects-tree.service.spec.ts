import { TestBed } from '@angular/core/testing';
import { ProjectsTreeService } from './projects-tree.service';
import { StateService } from '../../../services/state/state.service';
import { ProjectsService } from '../../../services/projects/projects.service';
import { ProjectState } from '../../../model/state';
import { Project, ProjectItemType } from '../../../model/project';
import { ProjectTreeTestUtils } from '../../../../test/project-tree.test.util';
import project = ProjectTreeTestUtils.project;
import request = ProjectTreeTestUtils.request;
import nodeIdProject = ProjectTreeTestUtils.nodeIdProject;
import nodeOfProject = ProjectTreeTestUtils.nodeOfProject;
import nodeOfRequest = ProjectTreeTestUtils.nodeOfRequest;
import nodeIdFolder = ProjectTreeTestUtils.nodeIdFolder;
import folder = ProjectTreeTestUtils.folder;
import nodeOfFolder = ProjectTreeTestUtils.nodeOfFolder;
import nodeIdRequest = ProjectTreeTestUtils.nodeIdRequest;
import { UserMessageService } from '../../../services/system/user-message.service';
import { RequestSessionService } from '../../../services/request-session/request-session.service';

describe('ProjectsTreeService', () => {
  let service: ProjectsTreeService;
  let projectsService: ProjectsService;
  let stateService: StateService;
  let requestSessionService: RequestSessionService;
  let userMessageService: UserMessageService;

  function setUp(testData: { projects?: Project[]; state?: ProjectState } = {}) {
    stateService = jasmine.createSpyObj<StateService>(
      'StateService',
      {
        ['loadProjectsState']: Promise.resolve(),
        ['updateItemState']: undefined,
        ['updateSelection']: undefined,
        ['clearSelection']: undefined,
        ['cleanUpProjectState']: undefined,
      },
      { ['state']: testData.state ?? { itemState: {} } },
    );
    projectsService = jasmine.createSpyObj<ProjectsService>(
      'ProjectsService',
      {
        ['loadProjects']: Promise.resolve(),
        ['move']: Promise.resolve(),
        ['allIds']: [],
      },
      { ['projects']: testData.projects ?? [] },
    );
    requestSessionService = jasmine.createSpyObj<RequestSessionService>('RequestSessionService', {
      ['select']: undefined,
    });
    userMessageService = jasmine.createSpyObj<UserMessageService>('UserMessageService', {
      ['confirm']: Promise.resolve(),
    });
    TestBed.configureTestingModule({
      providers: [
        ProjectsTreeService,
        {
          provide: ProjectsService,
          useValue: projectsService,
        },
        {
          provide: StateService,
          useValue: stateService,
        },
        {
          provide: RequestSessionService,
          useValue: requestSessionService,
        },
        {
          provide: UserMessageService,
          useValue: userMessageService,
        },
      ],
    });
    service = TestBed.inject(ProjectsTreeService);
  }

  describe('Create projects tree', () => {
    it('should create the projects tree', async () => {
      const requestA1 = request('a1');
      const projectA = project('a').withRequest(requestA1);
      setUp({
        projects: [projectA],
        state: {
          itemState: { [nodeIdProject(projectA)]: { expanded: true } },
        },
      });
      await service.initialise();
      expect(service.treeNodes).toEqual([
        nodeOfProject(projectA),
        nodeOfRequest(requestA1, { level: 1 }),
      ]);
    });

    it('should create the projects tree with expanded folder', async () => {
      const request1 = request('r1');
      const request2 = request('r2');
      const folder1 = folder('f1').withRequest(request2);
      const project1 = project('p1').withRequest(request1).withFolder(folder1);
      setUp({
        projects: [project1],
        state: {
          itemState: {
            [nodeIdProject(project1)]: { expanded: true },
            [nodeIdFolder(folder1)]: { expanded: true },
          },
        },
      });
      await service.initialise();
      expect(service.treeNodes).toEqual([
        nodeOfProject(project1),
        nodeOfRequest(request1, { level: 1 }),
        nodeOfFolder(folder1, { level: 1 }),
        nodeOfRequest(request2, { level: 2 }),
      ]);
    });

    it('only a request node is leaf', async () => {
      const request1 = request('r1');
      const folder1 = folder('f1').withRequest(request1);
      const project1 = project('p1').withFolder(folder1);
      setUp({
        projects: [project1],
      });
      await service.initialise();
      expect(service.isLeaf(nodeOfProject(project1))).toBeFalse();
      expect(service.isLeaf(nodeOfFolder(folder1, { level: 1 }))).toBeFalse();
      expect(service.isLeaf(nodeOfRequest(request1, { level: 2 }))).toBeTrue();
    });
  });

  describe('Read tree operations', () => {
    const request1 = request('r1');
    const request2 = request('r2');
    const request3 = request('r3');
    const request4 = request('r4');
    const folder1 = folder('f1').withRequest(request1).withRequest(request2).withRequest(request3);
    const project1 = project('p1').withRequest(request4).withFolder(folder1);
    const request5 = request('r5');
    const request6 = request('r6');
    const project2 = project('p2').withRequest(request5).withRequest(request6);

    it('expands a node to show children', async () => {
      setUp({
        projects: [project1, project2],
      });
      await service.initialise();

      expect(service.treeNodes).toEqual([nodeOfProject(project1), nodeOfProject(project2)]);
      await service.expand(nodeOfProject(project2));
      expect(service.treeNodes).toEqual([
        nodeOfProject(project1),
        nodeOfProject(project2),
        nodeOfRequest(request5, { level: 1 }),
        nodeOfRequest(request6, { level: 1 }),
      ]);
    });

    it('collapses a node hiding children', async () => {
      setUp({
        projects: [project1, project2],
        state: {
          itemState: {
            [nodeIdProject(project1)]: { expanded: true },
            [nodeIdFolder(folder1)]: { expanded: true },
          },
        },
      });
      await service.initialise();

      expect(service.treeNodes).toEqual([
        nodeOfProject(project1),
        nodeOfRequest(request4, { level: 1 }),
        nodeOfFolder(folder1, { level: 1 }),
        nodeOfRequest(request1, { level: 2 }),
        nodeOfRequest(request2, { level: 2 }),
        nodeOfRequest(request3, { level: 2 }),
        nodeOfProject(project2),
      ]);
      await service.collapse(nodeOfProject(project1));
      expect(service.treeNodes).toEqual([nodeOfProject(project1), nodeOfProject(project2)]);
    });

    it('create children for a project', async () => {
      setUp({
        projects: [project1, project2],
      });
      await service.initialise();

      const project1Children = await service.children(nodeOfProject(project1));
      expect(project1Children).toEqual([
        nodeOfRequest(request4, { level: 1 }),
        nodeOfFolder(folder1, { level: 1 }),
      ]);
    });

    it('create children for a folder', async () => {
      setUp({
        projects: [project1, project2],
      });
      await service.initialise();

      const folder1Children = await service.children(nodeOfFolder(folder1, { level: 1 }));
      expect(folder1Children).toEqual([
        nodeOfRequest(request1, { level: 2 }),
        nodeOfRequest(request2, { level: 2 }),
        nodeOfRequest(request3, { level: 2 }),
      ]);
    });

    it('determines the parent of a visible folder', async () => {
      setUp({
        projects: [project1, project2],
      });
      await service.initialise();

      await service.expand(nodeOfProject(project1));
      const folder1Parent = await service.parentOf(nodeOfFolder(folder1, { level: 1 }));
      expect(folder1Parent).toEqual(nodeOfProject(project1));
    });

    it('determines the parent of visible requests', async () => {
      setUp({
        projects: [project1, project2],
      });
      await service.initialise();

      await service.expand(nodeOfProject(project1));
      await service.expand(nodeOfFolder(folder1, { level: 1 }));
      const request1Parent = await service.parentOf(nodeOfRequest(request1, { level: 2 }));
      const request2Parent = await service.parentOf(nodeOfRequest(request2, { level: 2 }));
      const request3Parent = await service.parentOf(nodeOfRequest(request3, { level: 2 }));
      expect(request1Parent).toEqual(nodeOfFolder(folder1, { level: 1 }));
      expect(request2Parent).toEqual(nodeOfFolder(folder1, { level: 1 }));
      expect(request3Parent).toEqual(nodeOfFolder(folder1, { level: 1 }));
    });

    it('should calculate the index from visible nodes (after expanding)', async () => {
      setUp({
        projects: [project1, project2],
      });
      await service.initialise();

      expect(service.indexOf(nodeOfProject(project1))).toBe(0);
      expect(service.indexOf(nodeOfProject(project2))).toBe(1);

      await service.expand(nodeOfProject(project1));

      expect(service.indexOf(nodeOfProject(project1))).toBe(0);
      expect(service.indexOf(nodeOfRequest(request4, { level: 1 }))).toBe(1);
      expect(service.indexOf(nodeOfFolder(folder1, { level: 1 }))).toBe(2);
      expect(service.indexOf(nodeOfProject(project2))).toBe(3);

      await service.expand(nodeOfFolder(folder1, { level: 1 }));

      expect(service.indexOf(nodeOfProject(project1))).toBe(0);
      expect(service.indexOf(nodeOfRequest(request4, { level: 1 }))).toBe(1);
      expect(service.indexOf(nodeOfFolder(folder1, { level: 1 }))).toBe(2);
      expect(service.indexOf(nodeOfRequest(request1, { level: 2 }))).toBe(3);
      expect(service.indexOf(nodeOfRequest(request2, { level: 2 }))).toBe(4);
      expect(service.indexOf(nodeOfRequest(request3, { level: 2 }))).toBe(5);
      expect(service.indexOf(nodeOfProject(project2))).toBe(6);

      await service.expand(nodeOfProject(project2));

      expect(service.indexOf(nodeOfProject(project1))).toBe(0);
      expect(service.indexOf(nodeOfRequest(request4, { level: 1 }))).toBe(1);
      expect(service.indexOf(nodeOfFolder(folder1, { level: 1 }))).toBe(2);
      expect(service.indexOf(nodeOfRequest(request1, { level: 2 }))).toBe(3);
      expect(service.indexOf(nodeOfRequest(request2, { level: 2 }))).toBe(4);
      expect(service.indexOf(nodeOfRequest(request3, { level: 2 }))).toBe(5);
      expect(service.indexOf(nodeOfProject(project2))).toBe(6);
      expect(service.indexOf(nodeOfRequest(request5, { level: 1 }))).toBe(7);
      expect(service.indexOf(nodeOfRequest(request6, { level: 1 }))).toBe(8);
    });
  });

  describe('Tree state', () => {
    const request1 = request('r1');
    const request2 = request('r2');
    const request3 = request('r3');
    const folder1 = folder('f1').withRequest(request1).withRequest(request2);
    const project1 = project('p1').withFolder(folder1).withRequest(request3);

    it('loads selection state', async () => {
      const state: ProjectState = {
        selectedItemId: nodeIdRequest(request2),
        itemState: {
          [nodeIdProject(project1)]: { expanded: true },
          [nodeIdFolder(folder1)]: { expanded: true },
        },
      };

      setUp({
        projects: [project1],
        state,
      });
      await service.initialise();

      expect(service.isSelected(nodeOfRequest(request1, { level: 2 }))).toBeFalse();
      expect(service.isSelected(nodeOfRequest(request2, { level: 2 }))).toBeTrue();
      expect(service.isSelected(nodeOfRequest(request3, { level: 1 }))).toBeFalse();
    });

    it('changes selection state', async () => {
      const state: ProjectState = {
        selectedItemId: nodeIdRequest(request2),
        itemState: {
          [nodeIdProject(project1)]: { expanded: true },
          [nodeIdFolder(folder1)]: { expanded: true },
        },
      };

      setUp({
        projects: [project1],
        state,
      });
      await service.initialise();

      await service.select(nodeOfRequest(request1, { level: 2 }));

      expect(stateService.updateSelection).toHaveBeenCalledOnceWith(nodeIdRequest(request1));
    });

    it('loads expand state', async () => {
      const state: ProjectState = {
        selectedItemId: nodeIdRequest(request2),
        itemState: {
          [nodeIdProject(project1)]: { expanded: true },
        },
      };

      setUp({
        projects: [project1],
        state,
      });
      await service.initialise();

      expect(service.isExpanded(nodeOfProject(project1))).toBeTrue();
      expect(service.isExpanded(nodeOfFolder(folder1, { level: 1 }))).toBeFalse();
      expect(service.isExpanded(nodeOfRequest(request3, { level: 1 }))).toBeFalse();
    });

    it('changes node expand state', async () => {
      const state: ProjectState = {
        selectedItemId: nodeIdRequest(request2),
        itemState: {},
      };
      setUp({
        projects: [project1],
        state,
      });
      await service.initialise();

      await service.expand(nodeOfProject(project1));

      expect(stateService.updateItemState).toHaveBeenCalledWith(nodeIdProject(project1), {
        expanded: true,
      });

      await service.expand(nodeOfFolder(folder1, { level: 1 }));

      expect(stateService.updateItemState).toHaveBeenCalledWith(nodeIdFolder(folder1), {
        expanded: true,
      });
    });
  });

  describe('Read tree indexes', () => {
    const request1 = request('r1');
    const request2 = request('r2');
    const request3 = request('r3');
    const request4 = request('r4');
    const request5 = request('r5');
    const folder1 = folder('f1').withRequest(request1).withRequest(request2).withRequest(request3);
    const project1 = project('p1').withRequest(request4).withFolder(folder1).withRequest(request5);
    const project2 = project('p2');

    beforeAll(async () => {
      setUp({
        projects: [project1, project2],
        state: {
          itemState: {
            [nodeIdProject(project1)]: { expanded: true },
            [nodeIdFolder(folder1)]: { expanded: true },
          },
        },
      });
    });

    beforeEach(async () => {
      await service.initialise();
    });

    it('should calculate the index of node at project parent', async () => {
      expect(service.indexAtParent(nodeOfRequest(request4, { level: 1 }))).toBe(0);
      expect(service.indexAtParent(nodeOfFolder(folder1, { level: 1 }))).toBe(1);
      expect(service.indexAtParent(nodeOfRequest(request5, { level: 1 }))).toBe(2);
    });

    it('should calculate the index of node at project parent skipping node', async () => {
      expect(
        service.indexAtParent(
          nodeOfFolder(folder1, { level: 1 }),
          nodeOfRequest(request4, { level: 1 }),
        ),
      ).toBe(0);
      expect(
        service.indexAtParent(
          nodeOfRequest(request5, { level: 1 }),
          nodeOfRequest(request4, { level: 1 }),
        ),
      ).toBe(1);
    });

    it('should calculate the index of node at project parent skipping node that does not apply', async () => {
      expect(
        service.indexAtParent(
          nodeOfFolder(folder1, { level: 1 }),
          nodeOfRequest(request5, { level: 1 }),
        ),
      ).toBe(1);
    });

    it('should calculate the index of node at folder parent', async () => {
      expect(service.indexAtParent(nodeOfRequest(request1, { level: 2 }))).toBe(0);
      expect(service.indexAtParent(nodeOfRequest(request2, { level: 2 }))).toBe(1);
      expect(service.indexAtParent(nodeOfRequest(request3, { level: 2 }))).toBe(2);
    });

    it('should calculate the index of node at folder parent skipping node', async () => {
      expect(
        service.indexAtParent(
          nodeOfRequest(request2, { level: 2 }),
          nodeOfRequest(request1, { level: 2 }),
        ),
      ).toBe(0);
      expect(
        service.indexAtParent(
          nodeOfRequest(request3, { level: 2 }),
          nodeOfRequest(request1, { level: 2 }),
        ),
      ).toBe(1);
    });

    it('should calculate the index of node at folder parent skipping node that does not apply', async () => {
      expect(
        service.indexAtParent(
          nodeOfRequest(request2, { level: 2 }),
          nodeOfRequest(request3, { level: 2 }),
        ),
      ).toBe(1);
    });

    it('should calculate the index of node at root', async () => {
      expect(service.indexAtParent(nodeOfProject(project1))).toBe(0);
      expect(service.indexAtParent(nodeOfProject(project2))).toBe(1);
    });

    it('should calculate the index of node at root skipping node', async () => {
      expect(service.indexAtParent(nodeOfProject(project2), nodeOfProject(project1))).toBe(0);
    });
  });

  describe('Move tree operations', () => {
    const request1 = request('r1');
    const request2 = request('r2');
    const request3 = request('r3');
    const folder1 = folder('f1').withRequest(request1).withRequest(request2);
    const project1 = project('p1').withRequest(request3).withFolder(folder1);
    const request4 = request('r4');
    const project2 = project('p2').withRequest(request4);

    it('Only projects can be dropped as root', async () => {
      setUp({ projects: [project1, project2] });
      await service.initialise();

      expect(service.allowsDropAsRoot(nodeOfProject(project1))).toBeTrue();
      expect(service.allowsDropAsRoot(nodeOfFolder(folder1, { level: 1 }))).toBeFalse();
      expect(service.allowsDropAsRoot(nodeOfRequest(request1, { level: 2 }))).toBeFalse();
    });

    it('Drop into rules ', async () => {
      setUp({ projects: [project1, project2] });
      await service.initialise();

      expect(
        service.allowsDropInto(nodeOfFolder(folder1, { level: 1 }), nodeOfProject(project2)),
      ).toBeTrue();
      expect(
        service.allowsDropInto(nodeOfRequest(request1, { level: 2 }), nodeOfProject(project2)),
      ).toBeTrue();
      expect(
        service.allowsDropInto(
          nodeOfRequest(request1, { level: 2 }),
          nodeOfFolder(folder1, { level: 1 }),
        ),
      ).toBeTrue();
      expect(
        service.allowsDropInto(
          nodeOfFolder(folder1, { level: 1 }),
          nodeOfFolder(folder1, { level: 1 }),
        ),
      ).toBeTrue();

      expect(service.allowsDropInto(nodeOfProject(project1), nodeOfProject(project2))).toBeFalse();
      expect(
        service.allowsDropInto(nodeOfProject(project1), nodeOfFolder(folder1, { level: 1 })),
      ).toBeFalse();
    });

    it('Moves a request to another project', async () => {
      setUp({ projects: [project1, project2] });
      await service.initialise();

      await service.move(
        nodeOfRequest(request4, { level: 1 }),
        2,
        nodeOfFolder(folder1, { level: 1 }),
      );

      expect(projectsService.move).toHaveBeenCalledOnceWith(
        { type: ProjectItemType.REQUEST, value: request4 },
        2,
        {
          type: ProjectItemType.FOLDER,
          value: folder1,
        },
      );
    });
  });
});

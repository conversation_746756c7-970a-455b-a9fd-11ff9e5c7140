import { Component, OnInit } from '@angular/core';
import { ProjectsTreeService } from './projects-tree.service';
import { faGlobe } from '@fortawesome/pro-regular-svg-icons';
import { faClipboardList, faPlusCircle } from '@fortawesome/pro-solid-svg-icons';
import { faChevronDown } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'kng-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.scss'],
  standalone: false,
})
export class ProjectsComponent implements OnInit {
  icons = {
    request: faGlobe,
    project: faClipboardList,
    new: faPlusCircle,
    down: faChevronDown,
  };

  constructor(public readonly projectsTreeService: ProjectsTreeService) {}

  async ngOnInit() {
    await this.projectsTreeService.initialise();
  }

  async createNewRequest() {
    await this.projectsTreeService.createNewScratchRequest();
  }

  async createNewProject() {
    await this.projectsTreeService.createNewProject();
  }
}

<div class="projects">
  <div class="projects--menu">
    <button mat-button color="primary" class="small" [matMenuTriggerFor]="menu">
      New…
      <fa-icon [icon]="icons.down"></fa-icon>
    </button>
    <button mat-button color="primary" class="small" (click)="createNewRequest()">
      <fa-icon [icon]="icons.new"></fa-icon>
      New request
    </button>
  </div>
  <div class="projects--tree">
    <kng-tree [dataSource]="projectsTreeService" [delegate]="projectsTreeService"></kng-tree>
  </div>
</div>

<mat-menu #menu="matMenu">
  <button mat-menu-item (click)="createNewRequest()">
    <fa-icon [icon]="icons.request"></fa-icon>
    New request
  </button>
  <button mat-menu-item (click)="createNewProject()">
    <fa-icon [icon]="icons.project"></fa-icon>
    New project
  </button>
</mat-menu>

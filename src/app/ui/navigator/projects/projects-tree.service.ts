import { Injectable } from '@angular/core';
import {
  TreeDataSource,
  TreeDelegate,
  TreeNode,
  TreeNodeContextAction,
  treeNodeContextActionSeparator,
} from '../../_common/tree/tree-node';
import { ProjectsService, SCRATCHES_PROJECT_ID } from '../../../services/projects/projects.service';
import { StateService } from '../../../services/state/state.service';
import {
  Project,
  ProjectFolder,
  ProjectItem,
  ProjectItemFolder,
  ProjectItemRequest,
  ProjectItemType,
} from '../../../model/project';
import { Request } from '../../../model/request';
import { UserMessageService } from '../../../services/system/user-message.service';
import { RequestSessionService } from '../../../services/request-session/request-session.service';
import {
  faFolderPlus,
  faGlobe,
  faPenToSquare,
  faTrashCan,
} from '@fortawesome/pro-regular-svg-icons';
import { faClipboardList, faFolder } from '@fortawesome/pro-solid-svg-icons';
import { focusAndSelect } from '../../_common/util/focus';
import { ImperfectUrl } from '../../../services/http/imperfect-url';

@Injectable()
export class ProjectsTreeService implements TreeDataSource, TreeDelegate {
  #nodes = new Array<TreeNode>();

  #renamingNode: TreeNode | undefined;

  static icons = {
    folder: faFolder,
    folderNew: faFolderPlus,
    request: faGlobe,
    project: faClipboardList,
    rename: faPenToSquare,
    delete: faTrashCan,
  };

  constructor(
    private readonly projectsService: ProjectsService,
    private readonly stateService: StateService,
    private readonly requestSessionService: RequestSessionService,
    private readonly messageService: UserMessageService,
  ) {}

  async initialise() {
    this.stateService.cleanUpProjectState(this.projectsService.allIds());
    await this.createTreeNodes();
    this.requestSessionService.requestUpdated?.subscribe((request) => {
      const nodeOfUpdatedRequest = this.#nodes.find(
        (node) =>
          node.type === TreeNodeType.REQUEST_NODE && (node.data as Request).id === request.id,
      );
      if (nodeOfUpdatedRequest && nodeOfUpdatedRequest.extraInfo != null) {
        nodeOfUpdatedRequest.extraInfo = this.requestNodeExtraInfo(request);
      }
    });
  }

  async createNewScratchRequest() {
    const itemRequest = await this.projectsService.createScratchRequest();
    await this.createTreeNodes();

    const scratchesProjectNode = this.#nodes.find(
      (node) =>
        node.type === TreeNodeType.PROJECT_NODE &&
        (node.data as Project).id === SCRATCHES_PROJECT_ID,
    );
    if (scratchesProjectNode) {
      await this.expand(scratchesProjectNode);
    }

    const newNode = this.#nodes.find(
      (node) =>
        node.type === TreeNodeType.REQUEST_NODE && (node.data as Request).id === itemRequest.id,
    );
    if (newNode) {
      this.select(newNode);
      this.beginRename(newNode);
    }
  }

  async createNewProject() {
    const newProject = await this.projectsService.createProject();
    if (newProject != null) {
      await this.createTreeNodes();
      const newProjectTreeNode = this.treeNodeFromProject(newProject);
      await this.expand(newProjectTreeNode);
      this.beginRename(newProjectTreeNode);
    }
  }

  async createNewRequest(parent: TreeNode) {
    const newRequest = await this.projectsService.createRequest(parent.data);
    if (newRequest != null) {
      await this.createTreeNodes();
      await this.expand(parent);
      const newRequestTreeNode = this.treeNodeFromRequest(newRequest, parent.level + 1);
      await this.select(newRequestTreeNode);
      await this.beginRename(newRequestTreeNode);
    }
  }

  async createProjectFolder(parent: TreeNode) {
    const newFolder = await this.projectsService.createProjectFolder(parent.data);
    if (newFolder != null) {
      await this.createTreeNodes();
      await this.expand(parent);
      const newFolderTreeNode = this.treeNodeFromProjectFolder(newFolder, parent.level + 1);
      await this.expand(newFolderTreeNode);
      await this.beginRename(newFolderTreeNode);
    }
  }

  private async createTreeNodes() {
    const rootNodes = this.projectsService.projects.map((p) => this.treeNodeFromProject(p));
    this.#nodes = await this.descendantsIfExpanded(rootNodes);

    this.ensureNodeSelection();
  }

  private ensureNodeSelection() {
    const alreadySelectedNode = this.#nodes.find(
      (node) => this.allowsSelection(node) && node.id === this.stateService.state.selectedItemId,
    );
    if (alreadySelectedNode != null) {
      this.applySelectedNode(alreadySelectedNode);
    } else {
      const firstSelectableNode = this.#nodes.find((node) => this.allowsSelection(node));
      if (firstSelectableNode != null) {
        this.select(firstSelectableNode);
      } else {
        this.stateService.clearSelection();
      }
    }
  }

  private treeNodeFromProject(project: Project): TreeNode {
    return {
      id: project.id,
      label: project.name,
      icon: ProjectsTreeService.icons.project,
      level: 0,
      data: project,
      type: TreeNodeType.PROJECT_NODE,
    };
  }

  private treeNodeFromProjectFolder(projectFolder: ProjectFolder, level: number): TreeNode {
    return {
      id: projectFolder.id,
      label: projectFolder.name,
      icon: ProjectsTreeService.icons.folder,
      level,
      data: projectFolder,
      type: TreeNodeType.PROJECT_FOLDER_NODE,
    };
  }

  private treeNodeFromRequest(request: Request, level: number): TreeNode {
    return {
      id: request.id,
      label: request.name,
      extraInfo: this.requestNodeExtraInfo(request),
      icon: ProjectsTreeService.icons.request,
      level,
      data: request,
      type: TreeNodeType.REQUEST_NODE,
    };
  }

  private requestNodeExtraInfo(request: Request): { info: string; style: string }[] {
    function extractPath() {
      try {
        const url = new URL(request.url);
        return url.pathname;
      } catch {
        const url = new ImperfectUrl(request.url);
        return url.withoutQueryParams();
      }
    }

    function urlPathSummary(): string {
      let path = '';
      try {
        path = extractPath();
        const pathElements = path.split('/').filter(Boolean);
        if (pathElements != null && pathElements.length > 2) {
          const abbreviatedPathElements = pathElements.slice(-2).map((elem) => {
            if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/.test(elem)) {
              return `${elem.slice(0, 4)}…${elem.slice(-4)}`;
            } else {
              return elem;
            }
          });
          path = `…/${abbreviatedPathElements.join('/')}`;
        }
      } catch {}
      return path;
    }
    return [
      {
        info: request.method,
        style: `projects-tree-info-method-${request.method}`,
      },
      {
        info: urlPathSummary(),
        style: 'projects-tree-path',
      },
    ];
  }

  get treeNodes(): Readonly<TreeNode[]> {
    return [...this.#nodes];
  }

  isLeaf(node: TreeNode) {
    return node.type === TreeNodeType.REQUEST_NODE;
  }

  isExpanded(node: TreeNode) {
    return this.stateService.state.itemState[node.id]?.expanded ?? false;
  }

  async expand(node: TreeNode) {
    if (this.isExpanded(node)) {
      return;
    }

    const nodeIndex = this.indexOf(node);
    if (nodeIndex == null) {
      return;
    }

    const childNodes = await this.children(node);
    this.#nodes.splice(nodeIndex + 1, 0, ...childNodes);

    this.stateService.updateItemState(node.id, { expanded: true });
  }

  async collapse(node: TreeNode) {
    if (!this.isExpanded(node)) {
      return;
    }

    const nodeIndex = this.indexOf(node);
    if (nodeIndex == null) {
      return;
    }

    const countNodesToHideAfterCollapsing = () => {
      let nodesToRemove = 0;
      while (true) {
        const nextIndex = nodeIndex + nodesToRemove + 1;
        const nextNode = this.#nodes[nextIndex];
        if (nextNode != null && nextNode.level > node.level) {
          nodesToRemove++;
        } else {
          break;
        }
      }
      return nodesToRemove;
    };
    this.#nodes.splice(nodeIndex + 1, countNodesToHideAfterCollapsing());

    this.stateService.updateItemState(node.id, { expanded: false });
  }

  async children(node: TreeNode): Promise<TreeNode[]> {
    let projectItems: ProjectItem[];
    switch (node.type) {
      case TreeNodeType.PROJECT_NODE: {
        projectItems = (node.data as Project).items;
        break;
      }
      case TreeNodeType.PROJECT_FOLDER_NODE: {
        projectItems = (node.data as ProjectFolder).items;
        break;
      }
      default:
        projectItems = [];
    }

    const children = projectItems.map((projectItem) => {
      switch (projectItem.type) {
        case ProjectItemType.FOLDER:
          return this.treeNodeFromProjectFolder(projectItem.value as ProjectFolder, node.level + 1);
        case ProjectItemType.REQUEST:
          return this.treeNodeFromRequest(projectItem.value as Request, node.level + 1);
      }
    });

    return await this.descendantsIfExpanded(children);
  }

  parentOf(node: TreeNode): TreeNode | undefined {
    const nodeIndex = this.indexOf(node);
    if (nodeIndex) {
      for (let i = nodeIndex - 1; i >= 0; i--) {
        const parentNode = this.#nodes[i];
        if (parentNode.level < node.level) {
          console.debug('Parent of', node.label, parentNode.label);
          return parentNode;
        }
      }
    }
    return undefined;
  }

  indexOf(node: TreeNode): number | undefined {
    const nodeIndex = this.#nodes.findIndex((n) => n.id === node.id);
    if (nodeIndex < 0) {
      return undefined;
    } else {
      return nodeIndex;
    }
  }

  indexAtParent(node: TreeNode, skippingNode?: TreeNode): number | undefined {
    const nodeIndex = this.indexOf(node);
    const parent = this.parentOf(node);
    const parentIndex = parent != null ? this.indexOf(parent) : -1;
    const parentLevel = parent != null ? parent.level : -1;
    if (parentIndex == null || nodeIndex == null || parentIndex >= nodeIndex) {
      return undefined;
    }

    let count = 0;
    for (let i = nodeIndex - 1; i > parentIndex; i--) {
      const currentNode = this.#nodes[i];
      if (currentNode.level === parentLevel + 1) {
        if (skippingNode == null || skippingNode.id !== currentNode.id) {
          count++;
        }
      }
    }
    console.debug('Index at parent', node.label, count);
    return count;
  }

  private async descendantsIfExpanded(children: TreeNode[]) {
    const grandChildren = children.flatMap(async (node: TreeNode) => {
      if (!this.isLeaf(node) && this.isExpanded(node)) {
        return [node, ...(await this.children(node))];
      } else {
        return [node];
      }
    });

    return (await Promise.all(grandChildren)).reduce(
      (previousValue, currentValue) => [...previousValue, ...currentValue],
      [],
    );
  }

  allowsSelection(node: TreeNode): boolean {
    return node.type === TreeNodeType.REQUEST_NODE;
  }

  isSelected(node: TreeNode): boolean {
    return this.stateService.state.selectedItemId === node.id;
  }

  select(node: TreeNode): void {
    this.stateService.updateSelection(node.id);
    this.applySelectedNode(node);
  }

  private applySelectedNode(node: TreeNode) {
    if (this.allowsSelection(node)) {
      if (node.type === TreeNodeType.REQUEST_NODE) {
        const request = node.data as Request;
        this.requestSessionService.select(request);
      }
    }
  }

  allowsRename(node: TreeNode): boolean {
    return node.data.id !== SCRATCHES_PROJECT_ID;
  }

  isRenaming(node: TreeNode): boolean {
    return this.#renamingNode?.id === node.id;
  }

  beginRename(node: TreeNode) {
    if (this.allowsRename(node)) {
      this.#renamingNode = node;
      focusAndSelect(() => this.renamingNodeInput(node));
    }
  }

  cancelRename(node: TreeNode): void {
    const input = this.renamingNodeInput(node);
    if (input) {
      input.value = node.data?.name;
    }
    this.#renamingNode = undefined;
  }

  async rename(node: TreeNode, newName: string) {
    if (!newName?.trim()) {
      return this.cancelRename(node);
    }

    this.#renamingNode = undefined;
    await this.projectsService.rename(node.data, newName);
    await this.createTreeNodes();
  }

  async delete(node: TreeNode) {
    await this.projectsService.delete(node.data);
  }

  // TreeDelegate

  contextActions(node: TreeNode): TreeNodeContextAction[] {
    switch (node.type) {
      case TreeNodeType.PROJECT_NODE:
        return this.contextActionsForProject(node);
      case TreeNodeType.PROJECT_FOLDER_NODE:
        return this.contextActionsForProjectFolder(node);
      case TreeNodeType.REQUEST_NODE:
        return this.contextActionsForRequest(node);
      default:
        return [];
    }
  }

  private contextAction: { [key: string]: TreeNodeContextAction } = {
    newRequest: {
      label: 'New request',
      action: async (node: TreeNode) => {
        await this.createNewRequest(node);
      },
      icon: ProjectsTreeService.icons.request,
      negative: false,
    },
    newFolder: {
      label: 'New folder',
      action: async (node: TreeNode) => {
        await this.createProjectFolder(node);
      },
      icon: ProjectsTreeService.icons.folderNew,
      negative: false,
    },
    rename: {
      label: 'Rename...',
      action: (node: TreeNode) => {
        this.beginRename(node);
      },
      icon: ProjectsTreeService.icons.rename,
      negative: false,
    },
  };

  private contextActionsForProject(node: TreeNode): TreeNodeContextAction[] {
    const isScratchesProject = node.data.id === SCRATCHES_PROJECT_ID;
    return [
      this.contextAction.newRequest,
      this.contextAction.newFolder,
      treeNodeContextActionSeparator(),
      { ...this.contextAction.rename, disabled: isScratchesProject },
      {
        label: 'Delete',
        action: () => {
          if (node.type === TreeNodeType.PROJECT_NODE) {
            this.messageService
              .confirm(
                {
                  message: `Delete project '${node.label}' and all its contents?`,
                  detail: 'Deleting is permanent and cannot be undone.',
                },
                'Delete',
              )
              .then(async () => {
                await this.delete(node);
                await this.createTreeNodes();
              });
          }
        },
        icon: ProjectsTreeService.icons.delete,
        negative: true,
        disabled: isScratchesProject,
      },
    ];
  }

  private contextActionsForProjectFolder(node: TreeNode): TreeNodeContextAction[] {
    return [
      this.contextAction.newRequest,
      this.contextAction.newFolder,
      treeNodeContextActionSeparator(),
      this.contextAction.rename,
      {
        label: 'Delete',
        action: () => {
          if (node.type === TreeNodeType.PROJECT_FOLDER_NODE) {
            this.messageService
              .confirm(
                {
                  message: `Delete folder '${node.label}' and all its contents?`,
                  detail: 'Deleting is permanent and cannot be undone.',
                },
                'Delete',
              )
              .then(async () => {
                await this.delete(node);
                await this.createTreeNodes();
              });
          }
        },
        icon: ProjectsTreeService.icons.delete,
        negative: true,
      },
    ];
  }

  private contextActionsForRequest(node: TreeNode): TreeNodeContextAction[] {
    return [
      this.contextAction.rename,
      {
        label: 'Delete',
        action: () => {
          if (node.type === TreeNodeType.REQUEST_NODE) {
            this.messageService
              .confirm(
                {
                  message: `Delete '${node.label}'?`,
                  detail: 'Deleting is permanent and cannot be undone.',
                },
                'Delete',
              )
              .then(async () => {
                await this.delete(node);
                await this.createTreeNodes();
              });
          }
        },
        icon: ProjectsTreeService.icons.delete,
        negative: true,
      },
    ];
  }

  private renamingNodeInput(node: TreeNode): HTMLInputElement | null {
    return document.getElementById(`projects-tree-node-${node.id}`) as HTMLInputElement;
  }

  allowsDrag(node: TreeNode): boolean {
    return true;
  }

  allowsDropAsRoot(node: TreeNode): boolean {
    return node.type === TreeNodeType.PROJECT_NODE;
  }

  allowsDropInto(node: TreeNode, parent: TreeNode): boolean {
    return (
      (node.type === TreeNodeType.REQUEST_NODE &&
        (parent.type === TreeNodeType.PROJECT_FOLDER_NODE ||
          parent.type === TreeNodeType.PROJECT_NODE)) ||
      (node.type === TreeNodeType.PROJECT_FOLDER_NODE &&
        parent.type === TreeNodeType.PROJECT_NODE) ||
      node.type === TreeNodeType.PROJECT_FOLDER_NODE
    );
  }

  async move(node: TreeNode, newIndex: number, newParent?: TreeNode | undefined) {
    console.debug('Moving', node.label, newIndex, newParent?.label);

    function asProjectOrItem(node: TreeNode): Project | ProjectItem {
      switch (node.type) {
        case TreeNodeType.PROJECT_NODE:
          return node.data as Project;
        case TreeNodeType.PROJECT_FOLDER_NODE:
          return {
            type: ProjectItemType.FOLDER,
            value: node.data,
          } as ProjectItemFolder;
        case TreeNodeType.REQUEST_NODE:
          return {
            type: ProjectItemType.REQUEST,
            value: node.data,
          } as ProjectItemRequest;
        default:
          throw new Error('Unsupported type ' + node.type);
      }
    }

    function asProjectOrFolder(
      node: TreeNode | undefined,
    ): Project | ProjectItemFolder | undefined {
      switch (node?.type) {
        case TreeNodeType.PROJECT_NODE:
          return node.data as Project;
        case TreeNodeType.PROJECT_FOLDER_NODE:
          return {
            type: ProjectItemType.FOLDER,
            value: node.data,
          } as ProjectItemFolder;
        default:
          return undefined;
      }
    }

    await this.projectsService.move(asProjectOrItem(node), newIndex, asProjectOrFolder(newParent));
    await this.createTreeNodes();
  }
}

export enum TreeNodeType {
  PROJECT_NODE,
  PROJECT_FOLDER_NODE,
  REQUEST_NODE,
}

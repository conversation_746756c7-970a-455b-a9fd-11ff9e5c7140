<div class="variable-new">
  <h2>New variable</h2>
  <div class="variable-new--view">
    <div class="variable-new--view--key">
      <label>Name</label>
      <input
        type="text"
        class="field"
        placeholder="Variable name"
        [maxlength]="maxVariableKeyLength"
        [(ngModel)]="variableKey"
        (keyup.enter)="createVariable()"
      />
    </div>
    <small class="variable-new--view--help" [ngClass]="{ error: invalidKeyInput }">
      @if (invalidKeyInput) {
        <fa-icon [icon]="icons.warning"></fa-icon>
      }
      Names must contain only letters, numbers, <code>-</code> and <code>_</code>
    </small>
  </div>
  <div class="variable-new--footer">
    <button mat-button color="primary" [disabled]="!isValid()" (click)="createVariable()">
      Create
    </button>
    <button mat-button (click)="cancel()">Cancel</button>
  </div>
</div>

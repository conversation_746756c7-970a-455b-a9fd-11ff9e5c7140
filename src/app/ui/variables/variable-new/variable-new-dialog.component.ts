import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Variable, VariableKey } from '../../../model/variable';
import { Project } from '../../../model/project';
import { VariablesService } from '../../../services/variables/variables.service';
import { UserMessageService } from '../../../services/system/user-message.service';
import { faTriangleExclamation } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'kng-variable-new-dialog',
  templateUrl: './variable-new-dialog.component.html',
  styleUrls: ['./variable-new-dialog.component.scss'],
  standalone: false,
})
export class VariableNewDialogComponent {
  variableKey?: string;
  maxVariableKeyLength = 80;

  icons = {
    warning: faTriangleExclamation,
  };

  constructor(
    private readonly dialogRef: MatDialogRef<VariableNewDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public readonly data: VariableNewDialogData | undefined,
    private readonly variablesService: VariablesService,
    private readonly userMessage: UserMessageService,
  ) {}

  private close(variableKey?: VariableKey) {
    this.dialogRef.close(variableKey);
  }

  isValid(): boolean {
    const variableKey = this.variableKey?.trim();
    if (!variableKey) {
      return false;
    }
    return /^[0-9a-z-_A-Z]+$/.test(variableKey);
  }

  get invalidKeyInput(): boolean {
    return !!this.variableKey && !this.isValid();
  }

  cancel() {
    this.close();
  }

  async createVariable() {
    if (this.isValid() && this.variableKey != null) {
      try {
        if (this.data?.project != null) {
          await this.variablesService.createProjectVariable(
            {
              key: this.variableKey.trim(),
            },
            this.data.project,
          );
        } else {
          await this.variablesService.createGlobalVariable({
            key: this.variableKey.trim(),
          } as Variable);
        }
        this.close(this.variableKey.trim());
      } catch (e: any) {
        await this.userMessage.error({
          title: 'Error creating variable',
          message: 'Cannot create variable',
          detail: e?.message,
        });
      }
    }
  }
}

export type VariableNewDialogData = {
  project?: Project;
};

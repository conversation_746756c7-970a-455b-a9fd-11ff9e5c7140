import { Component } from '@angular/core';
import { RequestSessionService } from '../../services/request-session/request-session.service';
import { VariablesService } from '../../services/variables/variables.service';
import { Variable, VariableKey } from '../../model/variable';
import { faAsterisk, faClipboardList } from '@fortawesome/pro-light-svg-icons';
import { faCirclePlus } from '@fortawesome/pro-solid-svg-icons';
import { faCircleQuestion } from '@fortawesome/free-solid-svg-icons';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import {
  VariableNewDialogComponent,
  VariableNewDialogData,
} from './variable-new/variable-new-dialog.component';
import { scrollIntoView } from '../_common/util/scroll';

@Component({
  selector: 'kng-variables-view',
  templateUrl: './variables-view-dialog.component.html',
  styleUrls: ['./variables-view-dialog.component.scss'],
  standalone: false,
})
export class VariablesViewDialogComponent {
  icons = {
    project: faClipboardList,
    global: faAsterisk,
    new: faCirclePlus,
    help: faCircleQuestion,
  };

  constructor(
    private readonly dialogRef: MatDialogRef<VariablesViewDialogComponent>,
    private readonly variablesService: VariablesService,
    private readonly requestSessionService: RequestSessionService,
    private readonly matDialog: MatDialog,
  ) {}

  get globalVariables(): Variable[] {
    return this.variablesService.variablesInGlobalScope();
  }

  get projectVariables(): Readonly<Variable[]> {
    return this.variablesService.variablesInProjectScope(this.project);
  }

  get project() {
    return this.requestSessionService.project;
  }

  addProjectVariable() {
    this.matDialog
      .open(VariableNewDialogComponent, {
        data: {
          project: this.project,
        } as VariableNewDialogData,
      })
      .afterClosed()
      .subscribe((result: VariableKey | undefined) => {
        scrollIntoView(() => `project-variable-${result}`);
      });
  }

  addGlobalVariable() {
    this.matDialog
      .open(VariableNewDialogComponent, {
        data: {
          project: undefined,
        } as VariableNewDialogData,
      })
      .afterClosed()
      .subscribe((result: VariableKey | undefined) => {
        scrollIntoView(() => `global-variable-${result}`);
      });
  }

  close() {
    this.dialogRef.close();
  }
}

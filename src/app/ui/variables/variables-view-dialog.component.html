<div class="variables">
  <h2>Variables</h2>
  <div class="variables--view">
    <kng-split-view id="variables-view" direction="vertical" [splitLimit]="100">
      @if (project != null) {
        <kng-split-view-1>
          @if (project) {
            <div class="variables--view--title">
              <h3>
                <fa-icon [icon]="icons.project"></fa-icon>
                Variables in project
                <em [title]="project.name">{{ project.name | truncate: 20 }}</em>
              </h3>
              <button mat-button color="primary" class="small" (click)="addProjectVariable()">
                <fa-icon [icon]="icons.new"></fa-icon>
                New project variable
              </button>
            </div>
          }
          @if (project) {
            <div class="variables--view--variables">
              @for (variable of projectVariables; track variable; let index = $index) {
                <kng-variable
                  [id]="'project-variable-' + variable.key"
                  [project]="project"
                  [variable]="variable"
                  [index]="index"
                  (copied)="close()"
                ></kng-variable>
              }
            </div>
          }
        </kng-split-view-1>
      }
      <kng-split-view-2>
        <div class="variables--view--title">
          <h3>
            <fa-icon [icon]="icons.global"></fa-icon>
            Global variables
          </h3>
          <button mat-button color="primary" class="small" (click)="addGlobalVariable()">
            <fa-icon [icon]="icons.new"></fa-icon>
            New global variable
          </button>
        </div>
        <div class="variables--view--variables">
          @for (variable of globalVariables; track variable; let index = $index) {
            <kng-variable
              [id]="'global-variable-' + variable.key"
              [variable]="variable"
              [index]="index"
              (copied)="close()"
            ></kng-variable>
          }
        </div>
      </kng-split-view-2>
    </kng-split-view>
  </div>
  <div class="variables--footer">
    <a href="https://headrest.io/articles/variables" target="_blank" rel="help">
      <fa-icon [icon]="icons.help"></fa-icon>
      How to use variables
    </a>
    <button mat-button (click)="close()">Close</button>
  </div>
</div>

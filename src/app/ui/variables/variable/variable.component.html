@if (editingVariable != null) {
  <div
    class="variable"
    [ngClass]="{
      expanded,
    }"
  >
    <div class="variable--summary">
      <div class="variable--summary--copy">
        <button
          mat-button
          class="small"
          (click)="copyVariableName()"
          title="Copy variable name to clipboard and close this dialog"
        >
          <fa-icon [icon]="icons.copy"></fa-icon>
        </button>
      </div>
      <div class="variable--summary--data" (click)="toggleExpand()">
        <div class="variable--summary--data--key">{{ editingVariable.key }}</div>
        <div class="variable--summary--data--value">
          {{ evaluateVariable(editingVariable) ?? "—" | truncate: 90 }}
        </div>
      </div>
      <div class="variable--summary--buttons">
        <button
          mat-button
          color="warn"
          class="small"
          title="Delete variable"
          (click)="deleteVariable()"
        >
          <fa-icon [icon]="icons.delete"></fa-icon>
        </button>
      </div>
    </div>
    @if (expanded) {
      <div class="variable--edit">
        <div class="variable--edit--header">
          <div class="variable--edit--header--name">Environment</div>
          <div class="variable--edit--header--default-value">Default value</div>
          <div class="variable--edit--header--current-value">Current value</div>
        </div>
        @for (environment of environments; track environment; let environmentIndex = $index) {
          <div
            class="variable--edit--environment"
            [ngClass]="{
              active: isActiveEnvironment(environment),
            }"
          >
            <div
              class="variable--edit--environment--name"
              [title]="environmentNameTooltipFor(environment)"
            >
              {{ environment.name | truncate: maxEnvironmentNameLength }}
            </div>
            <div class="variable--edit--environment--default-value">
              <input
                #staticValueRef
                class="field"
                [ngModel]="getStaticValue(editingVariable, environment)"
                (ngModelChange)="setStaticValue(editingVariable, environment, $event)"
                (keydown)="staticValueKeyDown($event)"
                [placeholder]="'Default value in ' + environment.name"
                [tabIndex]="index * (environmentIndex + 1)"
                autocomplete="off"
                autocorrect="off"
                autocapitalize="off"
                spellcheck="false"
              />
            </div>
            <div class="variable--edit--environment--current-value">
              <input
                class="field"
                [value]="evaluateVariable(editingVariable, environment) ?? ''"
                (keydown)="currentValueKeyDown($event)"
                readonly
                tabindex="-1"
                placeholder="—"
              />
            </div>
          </div>
        }
        <div class="variable--edit--buttons">
          <button mat-button color="primary" (click)="saveVariable()">Save</button>
          <button mat-button (click)="collapse()">Cancel</button>
        </div>
      </div>
    }
  </div>
}

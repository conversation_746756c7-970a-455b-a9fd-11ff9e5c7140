import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { Variable, VariableValue } from '../../../model/variable';
import { Environment } from '../../../model/environment';
import { VariablesService } from '../../../services/variables/variables.service';
import { EnvironmentsService } from '../../../services/variables/environments.service';
import { Project } from '../../../model/project';
import { faPencil, faTrashCan } from '@fortawesome/pro-light-svg-icons';
import { faCopy } from '@fortawesome/free-regular-svg-icons';
import { focus } from '../../_common/util/focus';
import { UserMessageService } from '../../../services/system/user-message.service';

@Component({
  selector: 'kng-variable',
  templateUrl: './variable.component.html',
  styleUrls: ['./variable.component.scss'],
  standalone: false,
})
export class VariableComponent implements OnInit {
  #expanded = false;
  editingVariable?: Variable;
  readonly maxEnvironmentNameLength = 15;

  @Input() variable?: Variable;
  @Input() project?: Project;
  @Input() index = 0;
  @Output() copied = new EventEmitter<void>();

  @ViewChild('staticValueRef') staticValueRef?: ElementRef;

  icons = {
    copy: faCopy,
    edit: faPencil,
    delete: faTrashCan,
  };

  constructor(
    private readonly variablesService: VariablesService,
    private readonly environmentsService: EnvironmentsService,
    private readonly userMessage: UserMessageService,
  ) {}

  ngOnInit(): void {
    this.resetEditingVariable();
  }

  private resetEditingVariable() {
    this.editingVariable = {
      ...this.variable,
      staticValues: { ...this.variable?.staticValues },
    } as Variable;
  }

  get environments(): Environment[] {
    return this.environmentsService.environments;
  }

  get activeEnvironment(): Environment {
    return this.environmentsService.activeEnvironment;
  }

  isActiveEnvironment(environment: Environment): boolean {
    return this.activeEnvironment.id === environment.id;
  }

  environmentNameTooltipFor(environment: Environment): string {
    if (this.isActiveEnvironment(environment)) {
      return 'This is the active environment';
    }
    if (environment.name.length > this.maxEnvironmentNameLength) {
      return environment.name;
    }
    return '';
  }

  getStaticValue(variable: Variable, environment: Environment): VariableValue | undefined {
    return variable.staticValues?.[environment.id];
  }

  get expanded(): boolean {
    return this.#expanded;
  }

  toggleExpand() {
    if (this.#expanded) {
      this.collapse();
    } else {
      this.expand();
    }
  }

  private expand() {
    this.#expanded = true;
    focus(() => this.staticValueRef);
  }

  collapse() {
    this.#expanded = false;
    this.resetEditingVariable();
  }

  async staticValueKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      this.collapse();
      event.cancelBubble = true;
      event.preventDefault();
    } else if (event.key === 'Enter') {
      await this.saveVariable();
      event.cancelBubble = true;
      event.preventDefault();
    }
  }

  async currentValueKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      this.collapse();
      event.cancelBubble = true;
      event.preventDefault();
    }
  }

  setStaticValue(variable: Variable, environment: Environment, staticValue: VariableValue) {
    if (variable.staticValues == null) {
      variable.staticValues = {};
    }
    variable.staticValues[environment.id] = staticValue;
  }

  evaluateVariable(variable: Variable, environment?: Environment): string | undefined {
    return this.variablesService.evaluateVariable(
      variable.key,
      this.project?.id,
      environment ? environment.id : this.environmentsService.activeEnvironment.id,
    );
  }

  async copyVariableName() {
    if (this.variable != null) {
      await navigator.clipboard.writeText(`{{${this.variable?.key}}}`);
      this.copied.emit();
    }
  }

  async saveVariable() {
    if (this.editingVariable != null) {
      if (this.project != null) {
        await this.variablesService.updateProjectVariable(this.editingVariable, this.project);
        this.collapse();
      } else {
        await this.variablesService.updateGlobalVariable(this.editingVariable);
        this.collapse();
      }
    }
  }

  async deleteVariable() {
    if (this.variable != null) {
      if (this.project != null) {
        await this.userMessage.confirm(
          {
            message: `Delete project variable '${this.variable.key}' and all its references?`,
            detail:
              'This will permanently delete the variable, as well as its references from this project and its values from all environments.',
          },
          'Delete',
        );
        try {
          await this.variablesService.deleteProjectVariable(this.variable.key, this.project);
        } catch (e: any) {
          await this.userMessage.error({
            title: 'Error deleting variable',
            message: 'Cannot delete project variable',
            detail: e?.message,
          });
        }
      } else {
        await this.userMessage.confirm(
          {
            message: `Delete global variable '${this.variable.key}' and all its references?`,
            detail:
              'This will permanently delete the variable, as well as its references from all projects and its values from all environments.',
          },
          'Delete',
        );
        try {
          await this.variablesService.deleteGlobalVariable(this.variable.key);
        } catch (e: any) {
          await this.userMessage.error({
            title: 'Error deleting variable',
            message: 'Cannot delete global variable',
            detail: e?.message,
          });
        }
      }
    }
  }
}

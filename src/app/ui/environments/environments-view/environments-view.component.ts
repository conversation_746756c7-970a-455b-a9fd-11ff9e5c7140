import { Component, EventEmitter, Output } from '@angular/core';
import { faPencil, faTrashCan } from '@fortawesome/pro-light-svg-icons';
import { faCirclePlus } from '@fortawesome/pro-solid-svg-icons';
import { Environment } from '../../../model/environment';
import { EnvironmentsService } from '../../../services/variables/environments.service';
import { faCircleQuestion } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'kng-environments-view',
  templateUrl: './environments-view.component.html',
  styleUrls: ['./environments-view.component.scss'],
  standalone: false,
})
export class EnvironmentsViewComponent {
  newEnvironment = false;

  @Output() close = new EventEmitter<void>();

  icons = {
    new: faCirclePlus,
    rename: faPencil,
    delete: faTrashCan,
    help: faCircleQuestion,
  };

  constructor(private readonly environmentsService: EnvironmentsService) {}

  get environments(): Environment[] {
    return this.environmentsService.environments;
  }

  addEnvironment() {
    this.newEnvironment = true;
  }

  closeEnvironment() {
    this.newEnvironment = false;
  }
}

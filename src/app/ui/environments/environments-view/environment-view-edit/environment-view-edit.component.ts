import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { Environment } from '../../../../model/environment';
import {
  faCircleXmark,
  faFloppyDisk,
  faPencil,
  faTrashCan,
} from '@fortawesome/pro-light-svg-icons';
import { UserMessageService } from '../../../../services/system/user-message.service';
import { focusAndSelect } from '../../../_common/util/focus';
import { EnvironmentsService } from '../../../../services/variables/environments.service';

@Component({
  selector: 'kng-environment-view-edit',
  templateUrl: './environment-view-edit.component.html',
  styleUrls: ['./environment-view-edit.component.scss'],
  standalone: false,
})
export class EnvironmentViewEditComponent implements OnInit {
  @Input() mode: 'new' | 'view' | 'edit' = 'new';
  @Input() environment?: Environment;
  @Output() complete = new EventEmitter();

  environmentName?: string;

  @ViewChild('environmentNameField') environmentNameField?: ElementRef;

  icons = {
    save: faFloppyDisk,
    cancel: faCircleXmark,
    edit: faPencil,
    delete: faTrashCan,
  };

  constructor(
    private readonly environmentsService: EnvironmentsService,
    private readonly userMessage: UserMessageService,
  ) {}

  ngOnInit(): void {
    this.setFieldsFromEnvironment();

    if (this.mode === 'new') {
      focusAndSelect(() => this.environmentNameField);
    }
  }

  private setFieldsFromEnvironment() {
    this.environmentName = this.environment?.name;
  }

  editEnvironment() {
    if (this.mode === 'view') {
      this.mode = 'edit';
      focusAndSelect(() => this.environmentNameField);
    }
  }

  async environmentNameKeyDown(event: KeyboardEvent) {
    const target = event.target as HTMLInputElement;
    const isEnter = event.key === 'Enter';
    const isEscape = event.key === 'Escape';

    if (isEnter || isEscape) {
      if (isEnter) {
        await this.saveEnvironment();
      } else if (isEscape) {
        this.cancel();
      }
      target.selectionStart = target.selectionEnd;
      target.blur();
      event.cancelBubble = true;
      event.preventDefault();
    }
  }

  async saveEnvironment() {
    switch (this.mode) {
      case 'new':
        return this.createNewEnvironment();
      case 'edit':
        return this.renameEnvironment();
      default:
        return;
    }
  }

  private async createNewEnvironment() {
    if (this.environmentName?.trim()) {
      try {
        await this.environmentsService.createEnvironment(this.environmentName.trim());
        this.cancel();
      } catch (e: any) {
        await this.userMessage.error({
          title: 'Error creating environment',
          message: 'Cannot create environment',
          detail: e?.message,
        });
        focusAndSelect(() => this.environmentNameField);
      }
    }
  }

  private async renameEnvironment() {
    if (this.environment != null && this.environmentName?.trim()) {
      try {
        const environment = {
          id: this.environment.id,
          name: this.environmentName,
        };
        await this.environmentsService.renameEnvironment(environment);
        this.cancel();
      } catch (e: any) {
        await this.userMessage.error({
          title: 'Error renaming environment',
          message: 'Cannot rename environment',
          detail: e?.message,
        });
        focusAndSelect(() => this.environmentNameField);
      }
    }
  }

  cancel() {
    this.setFieldsFromEnvironment();
    this.mode = 'view';
    this.complete.emit();
  }

  async deleteEnvironment() {
    if (this.environment != null) {
      await this.userMessage.confirm(
        {
          message: `Delete environment '${this.environment.name}' and all its data?`,
          detail: 'This will permanently delete the environment, as well as its data.',
        },
        'Delete',
      );
      try {
        await this.environmentsService.deleteEnvironment(this.environment.id);
      } catch (e: any) {
        await this.userMessage.error({
          title: 'Error deleting environment',
          message: 'Cannot delete environment',
          detail: e?.message,
        });
      }
    }
  }
}

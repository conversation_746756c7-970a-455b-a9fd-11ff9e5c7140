<div
  class="environment-row"
  [ngClass]="{
    new: mode === 'new',
    edit: mode === 'edit',
  }"
>
  <div>
    <input
      #environmentNameField
      [(ngModel)]="environmentName"
      (keydown)="environmentNameKeyDown($event)"
      (dblclick)="editEnvironment()"
      placeholder="Environment name"
      [readOnly]="mode === 'view'"
      autocomplete="off"
      autocorrect="off"
      autocapitalize="off"
      spellcheck="false"
    />
  </div>
  <div>
    @if (mode === "new" || mode === "edit") {
      <button
        mat-button
        color="primary"
        class="small"
        title="Save environment"
        (click)="saveEnvironment()"
      >
        <fa-icon [icon]="icons.save"></fa-icon>
      </button>
    }
    @if (mode === "new" || mode === "edit") {
      <button mat-button class="small" title="Cancel changes" (click)="cancel()">
        <fa-icon [icon]="icons.cancel"></fa-icon>
      </button>
    }
    @if (mode === "view") {
      <button
        mat-button
        color="primary"
        class="small"
        title="Rename environment"
        (click)="editEnvironment()"
      >
        <fa-icon [icon]="icons.edit"></fa-icon>
      </button>
    }
    @if (mode === "view") {
      <button
        mat-button
        color="warn"
        class="small"
        title="Delete environment"
        (click)="deleteEnvironment()"
      >
        <fa-icon [icon]="icons.delete"></fa-icon>
      </button>
    }
  </div>
</div>

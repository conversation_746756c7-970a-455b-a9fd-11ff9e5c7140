<div class="environments">
  <h2>Environments</h2>
  <div class="environments--view">
    <div class="environments--view--title">
      <button mat-button color="primary" class="small" (click)="addEnvironment()">
        <fa-icon [icon]="icons.new"></fa-icon>
        New environment
      </button>
    </div>
    <div class="environments--view--data">
      @if (newEnvironment) {
        <kng-environment-view-edit (complete)="closeEnvironment()"></kng-environment-view-edit>
      }
      @for (environment of environments; track environment) {
        <kng-environment-view-edit
          mode="view"
          [environment]="environment"
        ></kng-environment-view-edit>
      }
    </div>
  </div>
  <div class="environments--footer">
    <a href="https://headrest.io/articles/environments" target="_blank" rel="help">
      <fa-icon [icon]="icons.help"></fa-icon>
      How to use environments
    </a>
    <button mat-button (click)="close.emit()">Close</button>
  </div>
</div>

import { Component } from '@angular/core';
import { faCheck, faChevronDown } from '@fortawesome/free-solid-svg-icons';
import { faLaptopCode } from '@fortawesome/pro-light-svg-icons';
import { EnvironmentsService } from '../../../services/variables/environments.service';
import { Environment } from '../../../model/environment';
import { MatDialog } from '@angular/material/dialog';
import { EnvironmentsDialogComponent } from '../environments-dialog/environments-dialog.component';

@Component({
  selector: 'kng-environment-selector',
  templateUrl: './environment-selector.component.html',
  styleUrls: ['./environment-selector.component.scss'],
  standalone: false,
})
export class EnvironmentSelectorComponent {
  icons = {
    down: faChevronDown,
    environment: faLaptopCode,
    selected: faCheck,
  };

  constructor(
    private readonly environmentsService: EnvironmentsService,
    private readonly matDialog: MatDialog,
  ) {}

  get environments(): EnvironmentWithActiveFlag[] {
    const activeEnvironment = this.environmentsService.activeEnvironment;
    return this.environmentsService.environments.map((environment) => {
      return {
        ...environment,
        active: environment.id === activeEnvironment.id,
      };
    });
  }

  get activeEnvironment(): string {
    return this.environmentsService.activeEnvironment.name;
  }

  manageEnvironments() {
    this.matDialog.open(EnvironmentsDialogComponent);
  }

  trackByEnvironment(index: number, item: EnvironmentWithActiveFlag) {
    return item.id;
  }

  async activateEnvironment(environment: Environment) {
    await this.environmentsService.select(environment);
  }
}

type EnvironmentWithActiveFlag = Environment & {
  active: boolean;
};

<div class="environment-selector">
  <button mat-button color="primary" class="small" [matMenuTriggerFor]="environmentsMenu">
    <strong [title]="activeEnvironment.length > 20 ? activeEnvironment : ''">
      {{ activeEnvironment | truncate: 20 }}
    </strong>
    environment
    <fa-icon [icon]="icons.down"></fa-icon>
  </button>
</div>

<mat-menu #environmentsMenu="matMenu">
  @for (environment of environments; track trackByEnvironment($index, environment)) {
    <button
      mat-menu-item
      [title]="environment.name.length > 20 ? environment.name : ''"
      (click)="activateEnvironment(environment)"
    >
      @if (!environment.active) {
        <span class="menu-icon-placeholder"></span>
      }
      @if (environment.active) {
        <fa-icon [icon]="icons.selected"></fa-icon>
      }
      {{ environment.name | truncate: 20 }}
    </button>
  }
  <div>
    <hr class="separator" />
  </div>
  <button mat-menu-item (click)="manageEnvironments()">
    <fa-icon [icon]="icons.environment"></fa-icon>
    Manage environments
  </button>
</mat-menu>

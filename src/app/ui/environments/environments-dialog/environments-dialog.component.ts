import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';

@Component({
  selector: 'kng-environments-dialog',
  templateUrl: './environments-dialog.component.html',
  styleUrls: ['./environments-dialog.component.scss'],
  standalone: false,
})
export class EnvironmentsDialogComponent {
  constructor(private readonly dialogRef: MatDialogRef<EnvironmentsDialogComponent>) {}

  close() {
    this.dialogRef.close();
  }
}

export const SystemServiceName = 'SystemService';

export interface ISystemService {
  ping(): Promise<any>;
  messageUser(message: UserMessage): Promise<boolean>;
  loadValue(name: string): Promise<string | undefined>;
  saveValue(name: string, jsonValue: string): Promise<void>;
}

export type UserMessage = {
  message: string;
  title?: string;
  detail?: string;
  confirm?: string;
  cancel: string;
  messageType: 'question' | 'error' | 'info';
};

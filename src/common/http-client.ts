export const HttpClientServiceName = 'HttpClientService';

export interface IHttpClientService {
  exchange(request: HttpRequest): Promise<HttpResponse>;
}

export type HttpRequest = {
  https: boolean;
  method: string;
  hostname: string;
  port?: string;
  path: string;
  query?: string;
  hash?: string;
  headers?: { [key: string]: string | undefined };
  payload?: string;
};

export type HttpResponse = {
  payload?: any;
  status: number;
  headers?: HttpResponseHeader[];
  info?: HttpResponseInfo;
};

export type HttpResponseInfo = {
  initiatedAt?: number;
  completedAt?: number;
};

export type HttpResponseHeader = {
  name: string;
  values?: string[];
};

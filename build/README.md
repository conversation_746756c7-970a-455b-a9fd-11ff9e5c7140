# Building for platforms

## Mac

For mac, we need to build for:

 - `dmg`, for local distribution
 - `mas`, for mac AppStore
 - `mas-dev`, for local testing of the mac AppStore build

All mac builds require Apple Developer certificates in the local keychain and specific provisioning profiles. They are not included in this git repo and the extensions are git-ignored.

[TODO] more work needs to be done here.

## Windows

[TODO]

## Linux

[TODO]

{"appId": "io.headrest.Headrest", "productName": "Headrest", "icon": "icon.icns", "copyright": "© 2022 Neovibrant LTD", "buildVersion": "10000", "files": ["node_modules", "node_modules/**", "dist/kingston-app", "dist/kingston-app/**", "dist/electron", "dist/electron/**"], "mac": {"target": "mas", "type": "distribution", "hardenedRuntime": false, "gatekeeperAssess": false, "category": "public.app-category.developer-tools", "icon": "icon.icns", "entitlements": "build/entitlements.mas.plist", "entitlementsInherit": "build/entitlements.mas.child.plist", "provisioningProfile": "build/headrest_dist.provisionprofile"}}
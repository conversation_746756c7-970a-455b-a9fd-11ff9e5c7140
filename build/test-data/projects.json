[{"id": "project-scratches", "name": "<PERSON><PERSON><PERSON>", "items": [{"type": 0, "value": {"id": "request-dff3fa10-d3ea-4fbf-ae84-43493fdde271", "name": "New request", "method": "GET", "url": "https://reqres.in/api/users?delay=1", "queryParams": [{"name": "delay", "value": "1"}], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "payload": {"type": 0, "rawPayload": {"type": "json", "body": ""}, "formPayload": {"params": []}}, "response": {"status": 200, "payload": "{\n  \"page\": 1,\n  \"per_page\": 6,\n  \"total\": 12,\n  \"total_pages\": 2,\n  \"data\": [\n    {\n      \"id\": 1,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"Bluth\",\n      \"avatar\": \"https://reqres.in/img/faces/1-image.jpg\"\n    },\n    {\n      \"id\": 2,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/2-image.jpg\"\n    },\n    {\n      \"id\": 3,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/3-image.jpg\"\n    },\n    {\n      \"id\": 4,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/4-image.jpg\"\n    },\n    {\n      \"id\": 5,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/5-image.jpg\"\n    },\n    {\n      \"id\": 6,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"Tracey\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/6-image.jpg\"\n    }\n  ],\n  \"support\": {\n    \"url\": \"https://reqres.in/#support-heading\",\n    \"text\": \"To keep ReqRes free, contributions towards server costs are appreciated!\"\n  }\n}", "info": {"completedAt": 1676901966706, "initiatedAt": 1676901965642}, "headers": [{"name": "Content-Encoding", "values": ["br"]}, {"name": "report-to", "values": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v3?s=H7iOzG09kTENZtJErPvJx7niTj80QCJgTazDQEWxYTOzaVFKdQorR6YAYpnuwsl7q3KJL3%2FRynkeW5poEKAi%2Fq8Q0ov6B%2F9X8AhYddFPmpfyNqaxwtJwbfyJRg%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}"]}, {"name": "cf-cache-status", "values": ["DYNAMIC"]}, {"name": "x-powered-by", "values": ["Express"]}, {"name": "Access-Control-Allow-Origin", "values": ["*"]}, {"name": "Content-Type", "values": ["application/json; charset=utf-8"]}, {"name": "Server", "values": ["cloudflare"]}, {"name": "nel", "values": ["{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}"]}, {"name": "Via", "values": ["1.1 vegur"]}, {"name": "Etag", "values": ["W/\"3e4-2RLXvr5wTg9YQ6aH95CkYoFNuO8\""]}, {"name": "Date", "values": ["Mon, 20 Feb 2023 14:06:06 GMT"]}, {"name": "cf-ray", "values": ["79c7cd0559a7742b-LHR"]}]}}}]}, {"id": "project-07e411cf-19a5-4500-ae79-b96bc32d55ea", "name": "Echo", "items": [{"type": 0, "value": {"id": "request-53433a9d-d2df-451f-ac86-d22868cd2b7b", "name": "Get Echo", "method": "GET", "url": "{{BASE_URL}}/get?val={{val}}", "queryParams": [{"name": "val", "value": "{{val}}"}], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}, {"name": "{{LANG_HEADER}}", "value": "{{locale}}"}], "payload": {"type": 0, "rawPayload": {"type": "json", "body": ""}, "formPayload": {"params": []}}, "response": {"status": 200, "payload": "{\n  \"args\": {\n    \"val\": \"ue\"\n  },\n  \"headers\": {\n    \"x-forwarded-proto\": \"https\",\n    \"x-forwarded-port\": \"443\",\n    \"host\": \"postman-echo.com\",\n    \"x-amzn-trace-id\": \"Root=1-63f79714-18ee360f7d1f96223cfb85d2\",\n    \"accept\": \"*/*\",\n    \"if-none-match\": \"W/\\\"235-1t79rZqhY8G/Nmvc1+H6FbciL1Q\\\"\",\n    \"user-agent\": \"Headrest/1\",\n    \"accept-language\": \"en-CA\",\n    \"accept-encoding\": \"gzip, deflate, br\",\n    \"cookie\": \"sails.sid=s%3Arn1eQk7igvsdR6szvLJl4R1N9K7KXRUK.iU0g1y7w%2B2rEqtsK9RzbM%2FrcX5zZfp29PrIotVGs5X4\"\n  },\n  \"url\": \"https://postman-echo.com/get?val=ue\"\n}", "info": {"completedAt": 1677170452510, "initiatedAt": 1677170452422}, "headers": [{"name": "Etag", "values": ["W/\"239-tACqsHTJFx1A+OQyDNaQsfhKMbY\""]}, {"name": "Date", "values": ["Thu, 23 Feb 2023 16:40:52 GMT"]}, {"name": "Set-<PERSON><PERSON>", "values": ["sails.sid=s%3AXUVvbs1i6fRMSWwqOiGK3PZb5DU2y8eW.3bVvl8uVflZD6dtf0CaQWjlbwwL5AGaGTCkVZFC96zQ; Path=/; HttpOnly"]}, {"name": "Content-Length", "values": ["569"]}, {"name": "Content-Type", "values": ["application/json; charset=utf-8"]}]}}}, {"type": 0, "value": {"id": "request-f99674c1-b59d-4d8b-9d6d-986b27b47dc4", "name": "Post Echo", "method": "POST", "url": "{{BASE_URL}}/post", "queryParams": [], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json"}], "payload": {"type": 1, "rawPayload": {"type": "json", "body": "{\n  \"val\": \"ue\"\n}"}, "formPayload": {"params": []}}, "response": {"status": 200, "payload": "{\n  \"args\": {},\n  \"data\": {\n    \"val\": \"ue\"\n  },\n  \"files\": {},\n  \"form\": {},\n  \"headers\": {\n    \"x-forwarded-proto\": \"https\",\n    \"x-forwarded-port\": \"443\",\n    \"host\": \"postman-echo.com\",\n    \"x-amzn-trace-id\": \"Root=1-6404e748-4d2dde723d1d9f3a2cd0f947\",\n    \"content-length\": \"17\",\n    \"accept\": \"*/*\",\n    \"content-type\": \"application/json\",\n    \"user-agent\": \"Headrest/1\",\n    \"accept-language\": \"en-GB;q=1.0\",\n    \"accept-encoding\": \"gzip, deflate, br\",\n    \"cookie\": \"sails.sid=s%3A0A3lNO3pZKBtK6ZHgvFXKNNNszVOySIw.Gwj%2BFCCgSmx3rtx5xjJIEPThlIrNH4zaqIxd4RbFFi4\"\n  },\n  \"json\": {\n    \"val\": \"ue\"\n  },\n  \"url\": \"https://postman-echo.com/post\"\n}", "info": {"completedAt": 1678042952622, "initiatedAt": 1678042952217}, "headers": [{"name": "Set-<PERSON><PERSON>", "values": ["sails.sid=s%3A9VUJBtRAxX3lXhIEhoDdYQ6pMXIaXty2.BsjFlDRc5t6%2Bl8oTjo3f9IUiEdlSapvYXw2AkWvxPxQ; Path=/; HttpOnly"]}, {"name": "Etag", "values": ["W/\"289-GIUXYg2mwP12h4GPNu5hpR22hmY\""]}, {"name": "Content-Length", "values": ["649"]}, {"name": "Content-Type", "values": ["application/json; charset=utf-8"]}, {"name": "Date", "values": ["Sun, 05 Mar 2023 19:02:32 GMT"]}]}, "setVariables": [{"variableKey": "val", "jsonPath": "$.data.val"}]}}, {"type": 0, "value": {"id": "request-e8461497-786d-46fb-8dcd-8987fda2ae95", "name": "Post form Echo", "method": "POST", "url": "{{BASE_URL}}/post", "queryParams": [], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "payload": {"type": 2, "rawPayload": {"type": "json", "body": ""}, "formPayload": {"params": [{"name": "{{valName}}", "value": "{{newVal}}"}, {"name": "locale", "value": "{{ZA}}"}]}}, "setVariables": [{"variableKey": "val", "jsonPath": "$.form.val"}, {"variableKey": "locale", "jsonPath": "$.form.locale"}], "response": {"status": 200, "payload": "{\n  \"args\": {},\n  \"data\": \"\",\n  \"files\": {},\n  \"form\": {\n    \"val\": \"uable\",\n    \"locale\": \"en-CA\"\n  },\n  \"headers\": {\n    \"x-forwarded-proto\": \"https\",\n    \"x-forwarded-port\": \"443\",\n    \"host\": \"postman-echo.com\",\n    \"x-amzn-trace-id\": \"Root=1-6404e739-6ff9c2ef32c63015545f60cc\",\n    \"content-length\": \"22\",\n    \"accept\": \"*/*\",\n    \"content-type\": \"application/x-www-form-urlencoded\",\n    \"user-agent\": \"Headrest/1\",\n    \"accept-language\": \"en-GB;q=1.0\",\n    \"accept-encoding\": \"gzip, deflate, br\"\n  },\n  \"json\": {\n    \"val\": \"uable\",\n    \"locale\": \"en-CA\"\n  },\n  \"url\": \"https://postman-echo.com/post\"\n}", "info": {"completedAt": 1678042938180, "initiatedAt": 1678042937003}, "headers": [{"name": "Etag", "values": ["W/\"260-VRMWNSPVyZwXJDEvVTmaA45IN5s\""]}, {"name": "Set-<PERSON><PERSON>", "values": ["sails.sid=s%3A0A3lNO3pZKBtK6ZHgvFXKNNNszVOySIw.Gwj%2BFCCgSmx3rtx5xjJIEPThlIrNH4zaqIxd4RbFFi4; Path=/; HttpOnly"]}, {"name": "Date", "values": ["Sun, 05 Mar 2023 19:02:17 GMT"]}, {"name": "Content-Length", "values": ["608"]}, {"name": "Content-Type", "values": ["application/json; charset=utf-8"]}]}}}, {"type": 1, "value": {"id": "folder-fb157da1-05f4-43f9-9244-764613949bed", "name": "Extras", "items": [{"type": 0, "value": {"id": "request-65f9322c-f5f8-4877-b50a-e58f297dfeca", "name": "Options Echo", "method": "OPTIONS", "url": "{{BASE_URL}}/get", "queryParams": [], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "payload": {"type": 0, "rawPayload": {"type": "json", "body": ""}, "formPayload": {"params": []}}, "response": {"status": 200, "payload": "GET,HEAD,PUT,POST,DELETE,PATCH", "info": {"completedAt": 1677170439510, "initiatedAt": 1677170439047}, "headers": [{"name": "allow", "values": ["GET,HEAD,PUT,POST,DELETE,PATCH"]}, {"name": "Etag", "values": ["W/\"1e-KLq1zJNrpa5FccGWE4HNnlYN8Rg\""]}, {"name": "Date", "values": ["Thu, 23 Feb 2023 16:40:39 GMT"]}, {"name": "Content-Length", "values": ["30"]}, {"name": "Set-<PERSON><PERSON>", "values": ["sails.sid=s%3Ay8QTsJBfhIhAE-80UFC_0uZERq1bR4Gk.sddQTQFMiANhX8N1O%2FFpNmmbOne%2BHBr%2BhxV2WH2s3go; Path=/; HttpOnly"]}, {"name": "Content-Type", "values": ["text/html; charset=utf-8"]}]}}}]}}]}, {"id": "project-00d97de2-3c22-49c9-a372-54afa90e46ff", "name": "User Management", "items": [{"type": 0, "value": {"id": "request-dff09152-0839-4231-a0da-c54600b4f6f0", "name": "Get users", "method": "GET", "url": "{{BASE_URL}}/api/users?email=<EMAIL>&first_name=<PERSON>&last_name=Bluth", "queryParams": [{"name": "email", "value": "<EMAIL>"}, {"name": "first_name", "value": "<PERSON>"}, {"name": "last_name", "value": "<PERSON><PERSON>"}, {"name": "avatar", "value": "https://reqres.in/img/faces/1-image.jpg", "disabled": true}], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}, {"name": "x-api-key", "value": "ee21041a-7ccd-4c8d-8116-cd3255e917d0", "disabled": true}], "payload": {"type": 0, "rawPayload": {"type": "json", "body": ""}, "formPayload": {"params": []}}, "setVariables": [{"variableKey": "AVATAR", "jsonPath": "$.data.[0].avatar"}], "response": {"status": 200, "payload": "{\n  \"page\": 1,\n  \"per_page\": 6,\n  \"total\": 12,\n  \"total_pages\": 2,\n  \"data\": [\n    {\n      \"id\": 1,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"Bluth\",\n      \"avatar\": \"https://reqres.in/img/faces/1-image.jpg\"\n    },\n    {\n      \"id\": 2,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/2-image.jpg\"\n    },\n    {\n      \"id\": 3,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/3-image.jpg\"\n    },\n    {\n      \"id\": 4,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/4-image.jpg\"\n    },\n    {\n      \"id\": 5,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/5-image.jpg\"\n    },\n    {\n      \"id\": 6,\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"Tracey\",\n      \"last_name\": \"<PERSON>\",\n      \"avatar\": \"https://reqres.in/img/faces/6-image.jpg\"\n    }\n  ],\n  \"support\": {\n    \"url\": \"https://reqres.in/#support-heading\",\n    \"text\": \"To keep ReqRes free, contributions towards server costs are appreciated!\"\n  }\n}", "info": {"completedAt": 1678045904186, "initiatedAt": 1678045904183}, "headers": [{"name": "Etag", "values": ["W/\"3e4-2RLXvr5wTg9YQ6aH95CkYoFNuO8\""]}, {"name": "Content-Type", "values": ["application/json; charset=utf-8"]}, {"name": "Date", "values": ["Sun, 05 Mar 2023 19:51:42 GMT"]}, {"name": "x-powered-by", "values": ["Express"]}, {"name": "Cache-Control", "values": ["max-age=14400"]}, {"name": "report-to", "values": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v3?s=fHqdEUAOtJ9Jx8c5XzOf1YKs%2BJSwa1Lc%2Fba0ReHXJ9Orj5IhprijyQn3aT7EaHbwkXs2U5TVRYZOxgobkgnzX3wegNK22hw0qQO4nhApcVEie2DW%2FCkXTewgUw%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}"]}, {"name": "Via", "values": ["1.1 vegur"]}, {"name": "Access-Control-Allow-Origin", "values": ["*"]}, {"name": "cf-cache-status", "values": ["MISS"]}, {"name": "nel", "values": ["{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}"]}, {"name": "Server", "values": ["cloudflare"]}, {"name": "cf-ray", "values": ["7a34e52caca7dcd3-LHR"]}, {"name": "Content-Encoding", "values": ["br"]}, {"name": "Vary", "values": ["Accept-Encoding"]}]}}}, {"type": 0, "value": {"id": "request-cd2d17a7-3ff8-4b61-ba34-fa098b867c83", "name": "Create user", "method": "POST", "url": "{{BASE_URL}}/api/users", "queryParams": [], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/json"}, {"name": "x-api-key", "value": "ee21041a-7ccd-4c8d-8116-cd3255e917d0"}], "payload": {"type": 1, "rawPayload": {"type": "json", "body": "{\n  \"fullName\": \"<PERSON>\",\n  \"avatar\": \"{{AVATAR}}\",\n  \"email\": \"<EMAIL>\",\n  \"roles\": {\n    \"admin\": [\"clients\", \"invoices\"],\n    \"readOnly\": [\"ALL\"],\n    \"exceptions\": [{\n      \"id\": 30924865,\n      \"applied\": true\n    }]\n  }\n}"}, "formPayload": {"params": []}}, "setVariables": [{"variableKey": "USER_ID", "jsonPath": "$.id"}], "response": {"status": 201, "payload": "{\n  \"fullName\": \"<PERSON>\",\n  \"avatar\": \"\",\n  \"email\": \"<EMAIL>\",\n  \"roles\": {\n    \"admin\": [\n      \"clients\",\n      \"invoices\"\n    ],\n    \"readOnly\": [\n      \"ALL\"\n    ],\n    \"exceptions\": [\n      {\n        \"id\": 30924865,\n        \"applied\": true\n      }\n    ]\n  },\n  \"id\": \"642\",\n  \"createdAt\": \"2023-03-05T19:15:18.487Z\"\n}", "info": {"completedAt": 1678043718479, "initiatedAt": 1678043718365}, "headers": [{"name": "cf-ray", "values": ["7a34afd80e68dd03-LHR"]}, {"name": "Access-Control-Allow-Origin", "values": ["*"]}, {"name": "Content-Length", "values": ["218"]}, {"name": "Via", "values": ["1.1 vegur"]}, {"name": "cf-cache-status", "values": ["DYNAMIC"]}, {"name": "x-powered-by", "values": ["Express"]}, {"name": "Content-Type", "values": ["application/json; charset=utf-8"]}, {"name": "Server", "values": ["cloudflare"]}, {"name": "report-to", "values": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v3?s=fVcye6pXdjVCSC7c3d1cDVwyAUb%2FQjBMDQHAFOieyJ0mGUSbuAX13rpOFQJTjN8Dl3lK24L85nJUOpqo8s8ftd0zWvrg4BWJHA678ATfchn5H1JK0Su0RKy24g%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}"]}, {"name": "nel", "values": ["{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}"]}, {"name": "Date", "values": ["Sun, 05 Mar 2023 19:15:18 GMT"]}, {"name": "Etag", "values": ["W/\"da-4hHTrTbspSYxnU0lBeJlLAUhaSM\""]}]}}}, {"type": 0, "value": {"id": "request-8a91abb1-2be0-4c38-be20-d5150e942bfb", "name": "Update user", "method": "PUT", "url": "https://reqres.in/api/users/{{USER_ID}}", "queryParams": [], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Type", "value": "application/x-www-form-urlencoded"}], "payload": {"type": 2, "rawPayload": {"type": "json", "body": ""}, "formPayload": {"params": []}}, "setVariables": []}}, {"type": 0, "value": {"id": "request-34e4e8b8-68d8-4331-a16d-368e76059307", "name": "Delete user", "method": "DELETE", "url": "https://reqres.in/api/users", "queryParams": [], "headers": [{"name": "User-Agent", "value": "Headrest/1"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Connection", "value": "keep-alive"}], "payload": {"type": 0, "rawPayload": {"type": "json", "body": ""}, "formPayload": {"params": []}}, "setVariables": [], "response": {"status": 204, "payload": "", "info": {"completedAt": 1678043018241, "initiatedAt": 1678043018146}, "headers": [{"name": "Server", "values": ["cloudflare"]}, {"name": "cf-ray", "values": ["7a349ebfcc0b2408-LHR"]}, {"name": "Etag", "values": ["W/\"2-vyGp6PvFo4RvsFtPoIWeCReyIC8\""]}, {"name": "Access-Control-Allow-Origin", "values": ["*"]}, {"name": "nel", "values": ["{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}"]}, {"name": "x-powered-by", "values": ["Express"]}, {"name": "Via", "values": ["1.1 vegur"]}, {"name": "cf-cache-status", "values": ["DYNAMIC"]}, {"name": "report-to", "values": ["{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v3?s=BPSkLrCOoYpCZl0MwjocJ9x279XCyNIIIfN0jQhlXOK9P16Rm%2FUEBz9GG6FoGD8XHLCuQWb%2BPl1ZdsCNLqjkkXhLtbZz%2FPE2VCrFSMfVsHh%2BCp4lU3eXtqV%2FhQ%3D%3D\"}],\"group\":\"cf-nel\",\"max_age\":604800}"]}, {"name": "Date", "values": ["Sun, 05 Mar 2023 19:03:38 GMT"]}]}}}]}]
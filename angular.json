{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"kingston-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "kng", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": {"base": "dist/kingston-app"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles/styles.scss"], "scripts": [], "browser": "src/main.ts", "stylePreprocessorOptions": {"includePaths": ["."]}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "20mb"}, {"type": "anyComponentStyle", "maximumWarning": "10mb", "maximumError": "20mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "kingston-app:build:production"}, "development": {"buildTarget": "kingston-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "kingston-app:build"}}, "test": {"builder": "@angular/build:karma", "options": {"karmaConfig": "karma.conf.js", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles/styles.scss"]}}, "cypress-run": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "kingston-app:serve"}, "configurations": {"production": {"devServerTarget": "kingston-app:serve:production"}}}, "cypress-open": {"builder": "@cypress/schematic:cypress", "options": {"watch": true, "headless": false}}, "e2e": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "kingston-app:serve", "watch": true, "headless": false}, "configurations": {"production": {"devServerTarget": "kingston-app:serve:production"}}}}}}, "defaultProject": "kingston-app", "cli": {"analytics": false}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}
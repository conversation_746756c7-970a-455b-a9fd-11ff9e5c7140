const path = require("path");

const rootPath = path.resolve(__dirname);

module.exports = {
  externals: ["mongodb-client-encryption"],
  resolve: {
    extensions: [".ts", ".js"],
  },
  devtool: "source-map",
  entry: path.resolve(rootPath, "src/electron", "main.ts"),
  target: "electron-main",
  module: {
    rules: [
      {
        test: /\.(js|ts)$/,
        exclude: /node_modules/,
        use: {
          loader: "ts-loader",
          options: {
            configFile: path.resolve(rootPath, "tsconfig.electron.json"),
          },
        },
      },
    ],
  },
  node: {
    __dirname: false,
  },
  output: {
    path: path.resolve(rootPath, "dist/electron"),
    filename: "main.js",
  },
};

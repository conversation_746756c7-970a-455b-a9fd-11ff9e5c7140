# kingston-app

## Building

The app is made of 2 parts:

- the Angular HTML/JavaScript app (that also runs in a browser)
- the "backend" that runs the Angular app as a desktop app

The 2 parts are built separately.

The backend has 3 implementations:
- Electron
- MasOS using WKWebView
- Pure web (limited by browser security, like CORS)

### Node/NPM versions

- NodeJS version 18.x.x 
- NPM version 8.x.x.

### Angular build

It builds like a normal Angular app running on the _client_ side.

```
npm run build
```

### Electron backend build

To build the Electron app, make sure you've built the Angular app first and then do:

```
npm run build:electron:prod
```

### MacOS backend build

To build the MacOS backend you need to be on a Mac and have Xcode installed. Make sure you've built the Angular app first.

- open the `platforms/macos/Headrest.xcodeproj` project in Xcode
- `Cmd + B` builds the project

### Web backend build

This is built as part of the Angular app, no further build is necessary.

## Running

### Running locally (dev mode)

- do a `npm install` if you haven't already
- start the Angular app with `npm run dev` and leave it running in the background to detect any UI changes you make and automatically refresh
- start the electron build with `npm run build:electron:dev` and leave it running in the background to detect any Electron changes you make and rebuild
- start the app by `npm run electron:dev`

#### For MacOS

- do a `npm install` if you haven't already
- remove previous builds with `rm -rf dist .angular`
- build the Angular app `npm run build`
- open Xcode and run with `Cmd + R`

## Distribution

### Electron

To create an executable/installer for your platform, run:

```
npm run dist
```

For Windows you will need to run:

```
npm run dist:win
```

## MacOS

- build Angular for production with `npm run build`
- open the `platforms/macos/Headrest.xcodeproj` project in Xcode
- run from Xcode for your platform in order to test locally
- create an Archive build for `All platforms` in Xcode
- validate the build
- distribute the build
  - via the Mac AppStore
  - independently via a Developer ID with Apple notarisation.

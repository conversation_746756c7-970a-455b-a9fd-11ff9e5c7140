//
//  WebView.swift
//  Headrest
//
//  Created by <PERSON> on 19/11/2022.
//

import Foundation
import Cocoa
import WebKit

class WebView: WKWebView {
    override func reload() -> WKNavigation? {
        super.reload()
        return load()
    }

    override func reloadFromOrigin() -> WKNavigation? {
        super.reloadFromOrigin()
        return load()
    }

    func load() -> WKNavigation? {
        let url = Bundle.main.url(forResource: "index", withExtension: "html", subdirectory: "kingston-app/browser")!
        return loadFileURL(url, allowingReadAccessTo: url)
//        let url = URL(string: "http://localhost:4200")!
//        return load(URLRequest(url: url))
    }

    override class var defaultMenu: NSMenu? {
        nil
    }

    override func willOpenMenu(_ menu: NSMenu, with event: NSEvent) {
        menu.items
                .map { item in
                    item.title
                }
                .forEach { title in
                    if let reloadMenuItem = menu.item(withTitle: title) {
                        menu.removeItem(reloadMenuItem)
                    }
                }
    }
}

//
//  ViewController.swift
//  Headrest
//
//  Created by <PERSON> on 30/10/2022.
//

import Cocoa
import WebKit
import os.log

class ViewController: <PERSON><PERSON>iew<PERSON><PERSON>roller, WKScriptMessageHandler, WKUIDelegate, WKNavigationDelegate {
    private let serviceRegistry = ServiceRegistry()

    override func viewDidLoad() {
        super.viewDidLoad()

        let webView = (view as! WebView)
        if #available(macOS 13.3, *) {
            webView.isInspectable = true
        }
        
        webView.configuration.preferences.setValue(true, forKey: "allowFileAccessFromFileURLs")

        let userScriptBaseApiUrl = WKUserScript(
            source: "function headrestMacosVersion() { return '\(Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "unknown")'; }",
            injectionTime: .atDocumentStart,
            forMainFrameOnly: true
        )
        webView.configuration.userContentController.addUserScript(userScriptBaseApiUrl)

        webView.configuration.userContentController.add(self, name: "headrestMacosCallAsync")

        _ = webView.load()
    }

    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        os_log(.debug, "Message received from page: \(message.name): \(String(describing: message.body))")
        switch message.name {
        case "headrestMacosCallAsync":
            let messageBody = message.body as? NSDictionary
            let name = (messageBody?["name"] as? String) ?? ""
            let callId = (messageBody?["callId"] as? String) ?? ""
            let args = (messageBody?["args"] as? NSDictionary) ?? NSDictionary()

            serviceRegistry.invoke(name: name, callId: callId, args: args) { result in
                let jsonResult = self.toJsonString(encodable: result)

                os_log(.debug, "Message response: \(name) (id: \(callId)): \(jsonResult)")

                let webView = (self.view as! WKWebView)
                webView.evaluateJavaScript("window.headrestMacosCallAsyncResponse('\(name)', '\(callId)', \(jsonResult));")
            }
        default:
            os_log(.error, "Message '\(message.name)' is not supported.")
        }
    }

    private func toJsonString(encodable result: Encodable?) -> String {
        if result == nil {
            return "undefined"
        }

        do {
            let data = try JSONEncoder().encode(result!)
            return String(data: data, encoding: .utf8)!
        } catch {
            os_log(.error, "\(error)")
            return "{_error: 'There was an error understanding the result of this operation.'}"
        }
    }
}


//
// Created by <PERSON> on 31/10/2022.
//

import Foundation
import Alamofire

class HttpClientService: Service {
    func invoke(methodName: String, args: NSDictionary, completion: @escaping (Encodable?) -> Void) {
        switch methodName {
        case "exchange":
            do {
                let httpRequest = args.to(HttpRequestArgs.self)
                try exchange(httpRequest: httpRequest.request, completion: completion)
            } catch {
                let errorMessage = error.localizedDescription
                completion(ErrorMessage(_error: errorMessage))
            }
        default:
            completion(ErrorMessage(_error: "Unsupported call to \(methodName) in HttpClientService"))
        }
    }

    private func exchange(httpRequest: HttpRequest, completion: @escaping (Encodable?) -> Void) throws {
        let url = try createUrl(httpRequest)
        try validateRequestPayload(httpRequest)
        var request = URLRequest(url: url)
        request.httpMethod = httpRequest.method
        request.httpBody = httpRequest.payload?.data(using: .utf8)
        httpRequest.headers
                .filter { key, value in
                    value != nil
                }
                .forEach { name, value in
                    request.addValue(value!, forHTTPHeaderField: name)
                }

        let startTimeMS = Int64(Date().timeIntervalSince1970 * 1000)

        AF.request(url) { request in
                    request.httpMethod = httpRequest.method
                    request.httpBody = httpRequest.payload?.data(using: .utf8)
                    httpRequest.headers
                            .filter { key, value in
                                value != nil
                            }
                            .forEach { name, value in
                                request.addValue(value!, forHTTPHeaderField: name)
                            }
                }
                .responseString { response in
                    let endTimeMS = Int64(Date().timeIntervalSince1970 * 1000)
                    let statusCode = response.response?.statusCode ?? 0
                    if statusCode > 0 {
                        let headers = self.headers(response.response)
                        var payload = response.value
                        let count = response.data?.count ?? 0
                        if payload == nil && count > 0 {
                            payload = "[The response returned \(count) bytes of binary data that cannot be displayed here]"
                        }
                        completion(HttpResponse(
                                payload: payload,
                                status: statusCode,
                                headers: headers,
                                info: HttpResponseInfo(initiatedAt: startTimeMS, completedAt: endTimeMS)
                        ))
                    } else {
                        let errorMessage = response.error?.localizedDescription ?? "An unknown error has occurred."
                        completion(ErrorMessage(_error: errorMessage))
                    }
                }
    }

    private func createUrl(_ request: HttpRequest) throws -> URL {
        let scheme = request.https ? "https://" : "http://"
        let port = request.port != nil && !request.port!.isEmpty ? ":\(request.port!)" : ""
        let hash = request.hash ?? ""
        let query = request.query ?? ""
        let urlString = "\(scheme)\(request.hostname)\(port)\(request.path)\(query)\(hash)";
        let url = URL(string: urlString)
        if let url = url {
            return url
        } else {
            throw UserError("The URL is not well formed.")
        }
    }

    private func validateRequestPayload(_ httpRequest: HttpRequest) throws {
        if httpRequest.method.lowercased() == "get" {
            if let payload = httpRequest.payload {
                if payload.count > 0 {
                    throw UserError("GET requests cannot have a body.")
                }
            }
        }
    }

    private func headers(_ response: HTTPURLResponse?) -> [HttpResponseHeader] {
        if let response {
            return response.allHeaderFields.map { header in
                let headerName: String = header.key.description
                let headerValue = response.value(forHTTPHeaderField: headerName)
                var headerValues: [String]? = nil
                if let headerValue {
                    headerValues = [headerValue]
                }
                return HttpResponseHeader(name: headerName, values: headerValues)
            }
        } else {
            return []
        }
    }
}

struct HttpRequestArgs: Codable {
    let request: HttpRequest
}

struct HttpRequest: Codable {
    let https: Bool
    let method: String
    let hostname: String
    let port: String?
    let path: String
    let query: String?
    let hash: String?
    let headers: Dictionary<String, String?>
    let payload: String?
}

struct HttpResponse: Encodable {
    let payload: String?
    let status: Int
    let headers: [HttpResponseHeader]
    let info: HttpResponseInfo?
}

struct HttpResponseHeader: Encodable {
    let name: String
    let values: [String]?
}

struct HttpResponseInfo: Encodable {
    let initiatedAt: Int64?
    let completedAt: Int64?
};

extension NSDictionary {
    func to<T>(_ type: T.Type) -> T where T: Decodable {
        let jsonData = try! JSONSerialization.data(withJSONObject: self)
        return try! JSONDecoder().decode(type, from: jsonData)
    }
}

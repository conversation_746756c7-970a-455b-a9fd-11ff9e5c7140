//
//  SystemService.swift
//  Headrest
//
//  Created by <PERSON> on 31/10/2022.
//

import Foundation
import AppKit
import os.log

class SystemService: Service {
    init() {
        let supportFolder = applicationSupportFolder()?.path ?? "nil"
        os_log(.debug, "Application Support folder: \(supportFolder)")
    }

    private func applicationSupportFolder() -> URL? {
        FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first
    }

    func invoke(methodName: String, args: NSDictionary, completion: @escaping (Encodable?) -> Void) {
        switch methodName {
        case "ping":
            completion(ping())
        case "messageUser":
            let userMessageArgs = args.to(UserMessageArgs.self)
            messageUser(message: userMessageArgs.message, completion: completion)
        case "loadValue":
            do {
                let loadValueArgs = args.to(LoadValue.self)
                let value = try loadValue(name: loadValueArgs.name)
                completion(value)
            } catch {
                let errorMessage = error.localizedDescription
                completion(ErrorMessage(_error: errorMessage))
            }
        case "saveValue":
            do {
                let saveValueArgs = args.to(SaveValue.self)
                try saveValue(name: saveValueArgs.name, value: saveValueArgs.value)
                completion(nil)
            } catch {
                let errorMessage = error.localizedDescription
                completion(ErrorMessage(_error: errorMessage))
            }
        default:
            completion(ErrorMessage(_error: "Unsupported call to \(methodName) in SystemService"))
        }
    }

    private func ping() -> Encodable? {
        Pong(name: "Pong", async: true)
    }

    private func showError(incomingError: IncomingError) {
        let errorTitle = incomingError.error.title ?? "Error"
        let errorDetail = incomingError.error.detail != nil ? " \n\n\(incomingError.error.detail!)" : ""
        let errorMessage = "\(incomingError.error.message)\(errorDetail)"
        DispatchQueue.main.async {
            let mainWindow = NSApplication.shared.mainWindow
            if let mainWindow {
                mainWindow.contentViewController?.presentError(
                    NSError(domain: incomingError.error.message, code: 0, userInfo: [
                        NSLocalizedDescriptionKey: errorTitle,
                        NSLocalizedRecoverySuggestionErrorKey: errorMessage,
                    ]),
                    modalFor: mainWindow,
                    delegate: nil,
                    didPresent: nil,
                    contextInfo: nil
                )
            }
        }
    }
    
    private func messageUser(message: UserMessage, completion: @escaping (Encodable?) -> Void) {
        DispatchQueue.main.async {
            let mainWindow = NSApplication.shared.mainWindow
            if let mainWindow {
                let alert = NSAlert()
                if let confirm = message.confirm {
                    alert.addButton(withTitle: confirm)
                }
                alert.addButton(withTitle: message.cancel)
                alert.messageText = message.message
                if let detail = message.detail {
                    alert.informativeText = detail
                }
                let originalTitle = mainWindow.title
                if let title = message.title {
                    mainWindow.title = title
                }
                switch message.messageType {
                case .info:
                    alert.alertStyle = .informational
                case .question:
                    alert.alertStyle = .warning
                case .error:
                    alert.alertStyle = .critical
                }
                alert.beginSheetModal(for: mainWindow) { response in
                    if message.title != nil {
                        mainWindow.title = originalTitle
                    }
                    completion(response == NSApplication.ModalResponse.alertFirstButtonReturn)
                }
            }
        }
    }

    private func loadValue(name: String) throws -> String? {
        if let folder = applicationSupportFolder() {
            let file = folder.appendingPathComponent("\(name).json")
            if FileManager.default.fileExists(atPath: file.path) {
                if let data = try? Data(contentsOf: file) {
                    return String(data: data, encoding: .utf8)
                }
            }
        }
        return nil
    }

    private func saveValue(name: String, value: String) throws {
        if let folder = applicationSupportFolder() {
            let file = folder.appendingPathComponent("\(name).json")
            let data = value.data(using: .utf8)
            if let data {
                if FileManager.default.fileExists(atPath: file.path) {
                    try? data.write(to: file, options: Data.WritingOptions.atomic)
                } else {
                    FileManager.default.createFile(atPath: file.path, contents: data)
                }
            }
        }
    }
}

struct Pong: Encodable {
    let name: String
    let async: Bool
}

struct IncomingError: Decodable {
    let error: IncomingErrorDetail
}

struct IncomingErrorDetail: Decodable {
    let message: String
    let detail: String?
    let title: String?
}

struct UserMessageArgs: Decodable {
    let message: UserMessage
}

struct UserMessage: Decodable {
  let message: String
  let title: String?
  let detail: String?
  let confirm: String?
  let cancel: String
  let messageType: UserMessageType
};

enum UserMessageType: String, Decodable {
    case question = "question"
    case error = "error"
    case info = "info"
}

struct LoadValue: Decodable {
    let name: String
}

struct SaveValue: Decodable {
    let name: String
    let value: String
}

//
// Created by <PERSON> on 31/10/2022.
//

import Foundation

class ServiceRegistry {
    private let systemService: Service = SystemService()
    private let httpClientService: Service = HttpClientService()

    func invoke(name: String, callId: String, args: NSDictionary, completion: @escaping (Encodable?) -> Void) {
        let nameArray = name.components(separatedBy: ".")
        let serviceName = nameArray.first ?? ""
        let methodName = nameArray.last ?? ""

        let onAsyncCompletion: (Encodable?) -> Void = { result in
            DispatchQueue.main.async {
                completion(result)
            }
        }

        DispatchQueue.global().async {
            switch serviceName {
            case "SystemService":
                self.systemService.invoke(methodName: methodName, args: args, completion: onAsyncCompletion)
            case "HttpClientService":
                self.httpClientService.invoke(methodName: methodName, args: args, completion: onAsyncCompletion)
            default:
                onAsyncCompletion(ErrorMessage(_error: "Unsupported call to \(name)"))
            }
        }
    }
}

struct ErrorMessage: Encodable {
    let _error: String
}

protocol Service {
    func invoke(methodName: String, args: NSDictionary, completion: @escaping (Encodable?) -> Void)
}

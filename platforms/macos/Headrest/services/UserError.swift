//
//  UserError.swift
//  Headrest
//
//  Created by <PERSON> on 23/11/2022.
//

import Foundation

class UserError: NSObject, LocalizedError {
    let errorMessage: String

    init(_ errorMessage: String) {
        self.errorMessage = errorMessage
    }
    override var description: String {
        get {
            "Error: \(errorMessage)"
        }
    }

    var errorDescription: String? {
        get {
            errorMessage
        }
    }
}

// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		DE245860501E70DD4D1A8521 /* HttpClientService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE24546921C925FFA91C5060 /* HttpClientService.swift */; };
		DE245B33A913DB7B4278BF00 /* ServiceRegistry.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE2452453066851F5E0CA01B /* ServiceRegistry.swift */; };
		DE245B53921FD4F2204A3661 /* SystemService.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE24568038781008DFF6D540 /* SystemService.swift */; };
		E70B09EF290FDD4B00888236 /* kingston-app in Resources */ = {isa = PBXBuildFile; fileRef = E70B09EE290FDD4B00888236 /* kingston-app */; };
		E72203632961CAF4001D3D95 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = E72203622961CAF4001D3D95 /* Alamofire */; };
		E7A4F6EB292DB24C009D9D84 /* UserError.swift in Sources */ = {isa = PBXBuildFile; fileRef = E7A4F6EA292DB24C009D9D84 /* UserError.swift */; };
		E7EAD088290EC3E100704C50 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = E7EAD087290EC3E100704C50 /* AppDelegate.swift */; };
		E7EAD08A290EC3E100704C50 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = E7EAD089290EC3E100704C50 /* ViewController.swift */; };
		E7EAD08C290EC3E200704C50 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = E7EAD08B290EC3E200704C50 /* Assets.xcassets */; };
		E7EAD08F290EC3E200704C50 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = E7EAD08D290EC3E200704C50 /* Main.storyboard */; };
		E7EBD84E2929110300E3F3C4 /* WebView.swift in Sources */ = {isa = PBXBuildFile; fileRef = E7EBD84D2929110300E3F3C4 /* WebView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		DE2452453066851F5E0CA01B /* ServiceRegistry.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ServiceRegistry.swift; sourceTree = "<group>"; };
		DE24546921C925FFA91C5060 /* HttpClientService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HttpClientService.swift; sourceTree = "<group>"; };
		DE24568038781008DFF6D540 /* SystemService.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SystemService.swift; sourceTree = "<group>"; };
		E70B09EE290FDD4B00888236 /* kingston-app */ = {isa = PBXFileReference; lastKnownFileType = folder; name = "kingston-app"; path = "../../../dist/kingston-app"; sourceTree = "<group>"; };
		E753CD552932CABC00797676 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		E7A4F6EA292DB24C009D9D84 /* UserError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserError.swift; sourceTree = "<group>"; };
		E7EAD084290EC3E100704C50 /* Headrest.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Headrest.app; sourceTree = BUILT_PRODUCTS_DIR; };
		E7EAD087290EC3E100704C50 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		E7EAD089290EC3E100704C50 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		E7EAD08B290EC3E200704C50 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		E7EAD08E290EC3E200704C50 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		E7EAD090290EC3E200704C50 /* Headrest.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Headrest.entitlements; sourceTree = "<group>"; };
		E7EBD84D2929110300E3F3C4 /* WebView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WebView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		E7EAD081290EC3E100704C50 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E72203632961CAF4001D3D95 /* Alamofire in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		E72654F529101DCB005AA542 /* services */ = {
			isa = PBXGroup;
			children = (
				DE2452453066851F5E0CA01B /* ServiceRegistry.swift */,
				DE24568038781008DFF6D540 /* SystemService.swift */,
				DE24546921C925FFA91C5060 /* HttpClientService.swift */,
				E7A4F6EA292DB24C009D9D84 /* UserError.swift */,
			);
			path = services;
			sourceTree = "<group>";
		};
		E7EAD07B290EC3E100704C50 = {
			isa = PBXGroup;
			children = (
				E7EAD086290EC3E100704C50 /* Headrest */,
				E7EAD085290EC3E100704C50 /* Products */,
			);
			sourceTree = "<group>";
		};
		E7EAD085290EC3E100704C50 /* Products */ = {
			isa = PBXGroup;
			children = (
				E7EAD084290EC3E100704C50 /* Headrest.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		E7EAD086290EC3E100704C50 /* Headrest */ = {
			isa = PBXGroup;
			children = (
				E753CD552932CABC00797676 /* Info.plist */,
				E72654F529101DCB005AA542 /* services */,
				E70B09EE290FDD4B00888236 /* kingston-app */,
				E7EAD087290EC3E100704C50 /* AppDelegate.swift */,
				E7EBD84D2929110300E3F3C4 /* WebView.swift */,
				E7EAD089290EC3E100704C50 /* ViewController.swift */,
				E7EAD08B290EC3E200704C50 /* Assets.xcassets */,
				E7EAD08D290EC3E200704C50 /* Main.storyboard */,
				E7EAD090290EC3E200704C50 /* Headrest.entitlements */,
			);
			path = Headrest;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		E7EAD083290EC3E100704C50 /* Headrest */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E7EAD093290EC3E200704C50 /* Build configuration list for PBXNativeTarget "Headrest" */;
			buildPhases = (
				E7EAD080290EC3E100704C50 /* Sources */,
				E7EAD081290EC3E100704C50 /* Frameworks */,
				E7EAD082290EC3E100704C50 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Headrest;
			packageProductDependencies = (
				E72203622961CAF4001D3D95 /* Alamofire */,
			);
			productName = Headrest;
			productReference = E7EAD084290EC3E100704C50 /* Headrest.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		E7EAD07C290EC3E100704C50 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1400;
				LastUpgradeCheck = 1400;
				TargetAttributes = {
					E7EAD083290EC3E100704C50 = {
						CreatedOnToolsVersion = 14.0.1;
					};
				};
			};
			buildConfigurationList = E7EAD07F290EC3E100704C50 /* Build configuration list for PBXProject "Headrest" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = E7EAD07B290EC3E100704C50;
			packageReferences = (
				E72203612961CAF4001D3D95 /* XCRemoteSwiftPackageReference "Alamofire" */,
			);
			productRefGroup = E7EAD085290EC3E100704C50 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				E7EAD083290EC3E100704C50 /* Headrest */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		E7EAD082290EC3E100704C50 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E70B09EF290FDD4B00888236 /* kingston-app in Resources */,
				E7EAD08C290EC3E200704C50 /* Assets.xcassets in Resources */,
				E7EAD08F290EC3E200704C50 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		E7EAD080290EC3E100704C50 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E7EAD08A290EC3E100704C50 /* ViewController.swift in Sources */,
				E7EAD088290EC3E100704C50 /* AppDelegate.swift in Sources */,
				DE245B33A913DB7B4278BF00 /* ServiceRegistry.swift in Sources */,
				E7EBD84E2929110300E3F3C4 /* WebView.swift in Sources */,
				DE245B53921FD4F2204A3661 /* SystemService.swift in Sources */,
				E7A4F6EB292DB24C009D9D84 /* UserError.swift in Sources */,
				DE245860501E70DD4D1A8521 /* HttpClientService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		E7EAD08D290EC3E200704C50 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				E7EAD08E290EC3E200704C50 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		E7EAD091290EC3E200704C50 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.12;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		E7EAD092290EC3E200704C50 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.12;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		E7EAD094290EC3E200704C50 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = Headrest/Headrest.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1040300;
				DEVELOPMENT_TEAM = 4HW7V9B3PY;
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Headrest/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Headrest;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.developer-tools";
				INFOPLIST_KEY_NSHumanReadableCopyright = "© 2025 Neovibrant Ltd";
				INFOPLIST_KEY_NSMainStoryboardFile = Main;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.4.3;
				PRODUCT_BUNDLE_IDENTIFIER = io.headrest.Headrest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		E7EAD095290EC3E200704C50 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CODE_SIGN_ENTITLEMENTS = Headrest/Headrest.entitlements;
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1040300;
				DEVELOPMENT_TEAM = 4HW7V9B3PY;
				ENABLE_HARDENED_RUNTIME = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Headrest/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = Headrest;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.developer-tools";
				INFOPLIST_KEY_NSHumanReadableCopyright = "© 2025 Neovibrant Ltd";
				INFOPLIST_KEY_NSMainStoryboardFile = Main;
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MARKETING_VERSION = 1.4.3;
				PRODUCT_BUNDLE_IDENTIFIER = io.headrest.Headrest;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		E7EAD07F290EC3E100704C50 /* Build configuration list for PBXProject "Headrest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E7EAD091290EC3E200704C50 /* Debug */,
				E7EAD092290EC3E200704C50 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		E7EAD093290EC3E200704C50 /* Build configuration list for PBXNativeTarget "Headrest" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				E7EAD094290EC3E200704C50 /* Debug */,
				E7EAD095290EC3E200704C50 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		E72203612961CAF4001D3D95 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = exactVersion;
				version = 5.10.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		E72203622961CAF4001D3D95 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = E72203612961CAF4001D3D95 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = E7EAD07C290EC3E100704C50 /* Project object */;
}

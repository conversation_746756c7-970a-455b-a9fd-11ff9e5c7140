{"name": "Headrest", "version": "1.4.3", "author": "Neovibrant LTD", "homepage": "https://headrest.io", "scripts": {"clean": "rm -rf dist .angular", "clean:win": "rd /S /Q dist .angular", "ng": "ng", "dev": "ng serve --configuration development", "cdev": "rm -rf dist .angular && ng serve --configuration development", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:ci": "ng test --karma-config=karma-ci.conf.js --no-progress", "prettier": "prettier -c ./src ./tests", "prettier:fix": "prettier -c ./src ./tests --write", "electron": "electron .", "electron:dev": "electron . --dev", "build:electron:dev": "webpack --watch --config webpack.electron.config.js --mode=development", "build:electron:prod": "rm -rf dist/electron && webpack --config webpack.electron.config.js --mode=production", "build:electron:prod:win": "rd /S /Q dist\\electron & webpack --config webpack.electron.config.js --mode=production", "pack": "electron-builder build", "pack:mac:dmg": "electron-builder --config build/_electron-builder-mac-dmg.json", "pack:mac:mas": "electron-builder --config build/_electron-builder-mac-mas.json", "pack:mac:mas:dev": "electron-builder --config build/_electron-builder-mac-mas-dev.json", "dist": "rm -rf dist && ng build && webpack --config webpack.electron.config.js --mode=production && electron-builder build", "dist:win": "rd /S /Q dist & ng build && webpack --config webpack.electron.config.js --mode=production && electron-builder build", "test:unit": "PLAYWRIGHT_PROJECT=unit playwright test --project=unit", "test:unit:ui": "PLAYWRIGHT_PROJECT=unit playwright test --project=unit --ui", "e2e": "PLAYWRIGHT_PROJECT=e2e playwright test --project=e2e", "e2e:ci": "PLAYWRIGHT_PROJECT=e2e playwright test --project=e2e", "e2e:headed": "PLAYWRIGHT_PROJECT=e2e playwright test --project=e2e --headed", "e2e:ui": "PLAYWRIGHT_PROJECT=e2e playwright test --project=e2e --ui", "test:all": "playwright test"}, "private": true, "main": "dist/electron/main.js", "build": {"appId": "io.headrest.Headrest", "productName": "Headrest", "icon": "icon.icns", "copyright": "© 2025 Neovibrant LTD", "files": ["node_modules", "node_modules/**", "dist/kingston-app/browser", "dist/kingston-app/browser/**", "dist/electron", "dist/electron/**"], "linux": {"target": ["deb", "AppImage", "snap"], "category": "Development", "maintainer": "headrest.io"}, "deb": {"packageName": "Headrest", "artifactName": "headrest.deb"}, "win": {"target": "APPX"}, "appx": {"identityName": "NeovibrantLtd.Headrest", "publisher": "CN=3464AD0C-C2F6-44D3-8793-CA44D194B004", "publisherDisplayName": "Neovibrant LTD"}, "snap": {"confinement": "strict", "summary": "Headrest", "grade": "stable", "description": "Headrest", "category": "Development"}, "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true}}, "engines": {"node": "24", "npm": "11"}, "prettier": {"printWidth": 100}, "dependencies": {"@angular/animations": "20", "@angular/cdk": "20", "@angular/common": "20", "@angular/compiler": "20", "@angular/core": "20", "@angular/forms": "20", "@angular/material": "20", "@angular/platform-browser": "20", "@angular/platform-browser-dynamic": "20", "@angular/router": "20", "@codemirror/lang-json": "6", "@codemirror/lang-xml": "6", "@codemirror/state": "6", "@fortawesome/angular-fontawesome": "2", "@fortawesome/fontawesome-svg-core": "6.5.1", "@fortawesome/free-regular-svg-icons": "6.5.1", "@fortawesome/free-solid-svg-icons": "6.5.1", "@fortawesome/pro-duotone-svg-icons": "file:./.local/fortawesome/pro-duotone-svg-icons.tar.gz", "@fortawesome/pro-light-svg-icons": "file:./.local/fortawesome/pro-light-svg-icons.tar.gz", "@fortawesome/pro-regular-svg-icons": "file:./.local/fortawesome/pro-regular-svg-icons.tar.gz", "@fortawesome/pro-solid-svg-icons": "file:./.local/fortawesome/pro-solid-svg-icons.tar.gz", "@fortawesome/pro-thin-svg-icons": "file:./.local/fortawesome/pro-thin-svg-icons.tar.gz", "@fortawesome/sharp-light-svg-icons": "file:./.local/fortawesome/sharp-light-svg-icons.tar.gz", "@fortawesome/sharp-regular-svg-icons": "file:./.local/fortawesome/sharp-regular-svg-icons.tar.gz", "@fortawesome/sharp-solid-svg-icons": "file:./.local/fortawesome/sharp-solid-svg-icons.tar.gz", "@fortawesome/sharp-thin-svg-icons": "file:./.local/fortawesome/sharp-thin-svg-icons.tar.gz", "axios": "1", "codemirror": "6", "got": "12", "jsonpath-plus": "10", "luxon": "3", "rxjs": "7", "tslib": "2", "tslog": "x", "uuid": "x", "zone.js": "0.15.1"}, "devDependencies": {"@angular-devkit/build-angular": "20", "@angular/build": "20", "@angular/cli": "20", "@angular/compiler-cli": "20", "@playwright/test": "1", "@types/jasmine": "x", "@types/luxon": "3", "@types/node": "24", "@types/uuid": "x", "electron": "37", "electron-builder": "26", "jasmine-core": "x", "karma": "x", "karma-chrome-launcher": "x", "karma-coverage": "x", "karma-jasmine": "x", "karma-jasmine-html-reporter": "x", "prettier": "3", "ts-loader": "9", "ts-node": "10", "typescript": "5.8.3", "webpack-cli": "5"}}
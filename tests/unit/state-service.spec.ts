import { test, expect } from '@playwright/test';
import { StateService } from '../../src/app/services/state/state.service';
import { ProjectState } from '../../src/app/model/state';

// Mock SystemService
class MockSystemService {
  private mockData: string | undefined;

  constructor(mockData?: ProjectState) {
    this.mockData = mockData ? JSON.stringify(mockData) : undefined;
  }

  async loadValue(name: string): Promise<string | undefined> {
    return this.mockData;
  }

  async saveValue(name: string, value: string): Promise<void> {
    // Mock implementation - do nothing
  }
}

test.describe('StateService', () => {
  async function setUp(state: ProjectState | undefined) {
    const mockSystemService = new MockSystemService(state);
    const service = new StateService(mockSystemService as any);
    await service.loadProjectsState();
    return service;
  }

  test('should clean up state of old ids', async () => {
    const service = await setUp({
      itemState: {
        i1: { expanded: true },
        i2: { expanded: false },
        i3: { expanded: true },
      },
    });
    
    service.cleanUpProjectState(['i2', 'i3']);
    const actual = service.state.itemState;
    
    expect(actual).toEqual({
      i2: { expanded: false },
      i3: { expanded: true },
    });
  });

  test('should not clean up anything if all ids are the same', async () => {
    const service = await setUp({
      itemState: {
        i1: { expanded: true },
        i2: { expanded: false },
        i3: { expanded: true },
      },
    });
    
    service.cleanUpProjectState(['i1', 'i2', 'i3']);
    const actual = service.state.itemState;
    
    expect(actual).toEqual({
      i1: { expanded: true },
      i2: { expanded: false },
      i3: { expanded: true },
    });
  });

  test('should clean everything up anything if all ids are different', async () => {
    const service = await setUp({
      itemState: {
        i1: { expanded: true },
        i2: { expanded: false },
        i3: { expanded: true },
      },
    });
    
    service.cleanUpProjectState(['c1', 'c2', 'c3']);
    const actual = service.state.itemState;
    
    expect(actual).toEqual({});
  });

  test('should handle empty state', async () => {
    const service = await setUp(undefined);
    
    service.cleanUpProjectState(['c1', 'c2', 'c3']);
    const actual = service.state.itemState;
    
    expect(actual).toEqual({});
  });
});
